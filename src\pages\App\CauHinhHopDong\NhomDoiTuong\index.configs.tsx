import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

export interface TableNhomDoiTuongDataType {
  key: string;
  sott?: number;
  ma?: string;
  ten?: string;
  ma_cha?: string;
  ma_dau?: string;
  mo_ta?: string;
  stt?: number;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const danhMucNhomDoiTuongColumns: TableProps<TableNhomDoiTuongDataType>["columns"] = [
  {title: "STT", dataIndex: "sott", key: "sott", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "left", ...defaultTableColumnsProps},
  {title: "Mã cha", dataIndex: "ma_cha", key: "ma_cha", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Mã đầu", dataIndex: "ma_dau", key: "ma_dau", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Mô tả", dataIndex: "mo_ta", key: "mo_ta", width: 200, align: "left", ...defaultTableColumnsProps},
  {title: "Thứ tự", dataIndex: "stt", key: "stt", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130, align: "center", ...defaultTableColumnsProps},
];
//Form
export interface IFormTimKiemNhomDoiTuongFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  trang_thai: IFormInput;
}
export const FormTimKiemNhomDoiTuong: IFormTimKiemNhomDoiTuongFieldsConfig = {
  ma: {
    component: "input",
    name: "ma",
    label: "Mã nhóm đối tượng",
    placeholder: "Mã nhóm đối tượng",
    width: "10%",
  },
  ten: {
    component: "input",
    name: "ten",
    label: "Tên nhóm đối tượng",
    placeholder: "Tên nhóm đối tượng",
    width: "20%",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
    width: "20%",
  },
};
// defaultFormValue tìm kiếm phân trang nhóm đối tượng
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams = {
  ma: "",
  ten: "",
  trang_thai: "",
  // trang: 1,
  // so_dong: 20,
};
export const TRANG_THAI = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngừng sử dụng", ma: "K"},
];

export const radioItemTrangThaiNhomDoiTuongTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
