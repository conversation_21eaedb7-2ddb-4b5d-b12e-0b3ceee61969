import {CloseOutlined, DownloadOutlined, InfoCircleOutlined, PlusCircleOutlined, PlusOutlined, UploadOutlined, WarningOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, ModalImportExcel} from "@src/components";
import type {IModalImportExcelRef, IExcelRowData} from "@src/components/ModalImportExcel";
import {defaultPaginationTableProps, fillRowTableEmpty, useAsyncAction} from "@src/hooks";
import {formatCurrencyUS} from "@src/utils";
import {Col, Form, message, Row, Table, Tabs} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef, useState} from "react";
import {Dayjs} from "dayjs";
import {useBaoHiemTaiSanContext} from "../../index.context";
import {ruleRequired, ThongTinDoiTuongBaoHiemTaiSanStepProps} from "../Constant";
import {
  defaultFormValueTimKiemDoiTuongBaoHiemTaiSan,
  FormTaoMoiDoiTuongBaoHiemTaiSan,
  FormTimKiemDoiTuongBaoHiemTaiSan,
  initFormFieldsDoiTuong,
  listHDVipSelect,
  tableDoiTuongColumn,
  TableDoiTuongColumnDataType,
  VI_TRI,
} from "./Constant";
// import {RenderDieuKhoanBoSungTable, RenderLoaiHinhNghiepVuTable} from "./index";

const {nd_tim} = FormTimKiemDoiTuongBaoHiemTaiSan;
const {ten, gcn, nhom_dt, latitude, longitude, gio_hl, gia_tri, ngay_hl, gio_kt, ngay_kt, ngay_cap, vip, so_id_dt, so_id, dk, dkbs, vi_tri, tinh_thanh, phuong_xa, dchi} =
  FormTaoMoiDoiTuongBaoHiemTaiSan;

const PAGE_SIZE = 10; // khai báo khác default cho vừa màn hình

const ThongTinDoiTuongBaoHiemTaiSanStepComponent = forwardRef<any, ThongTinDoiTuongBaoHiemTaiSanStepProps>(
  ({formNhapDoiTuongBaoHiemTaiSan: formNhapDoiTuongBaoHiemTaiSan}: ThongTinDoiTuongBaoHiemTaiSanStepProps, ref) => {
    useImperativeHandle(ref, () => ({
      resetForm: () => {},
    }));

    const {
      loading,
      tongSoDongDoiTuongBaoHiemTaiSan,
      tongPhiBaoHiemTaiSanFromAPI,
      danhSachDoiTuongBaoHiemTaiSan,
      timKiemPhanTrangDoiTuongBaoHiemTaiSan,
      getListPhuongXa,
      setListPhuongXa,
      listTinhThanh,
      listPhuongXa,
      // layChiTietDoiTuongBaoHiemTaiSan,
      chiTietHopDongBaoHiemTaiSan,
      listNhomDoiTuong,
      updateDoiTuongBaoHiemTaiSan,
      layChiTietDoiTuongBaoHiemTaiSan,
      // exportExcel,
    } = useBaoHiemTaiSanContext();
    const [formTimKiemPhanTrangDoiTuongBaoHiemTaiSan] = Form.useForm();

    const [searchParams, setSearchParams] = useState<ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams>(
      defaultFormValueTimKiemDoiTuongBaoHiemTaiSan as ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams,
    );
    const [page, setPage] = useState(1);
    const [doiTuongTaiSanSelected, setDoiTuongTaiSanSelected] = useState<CommonExecute.Execute.IDoiTuongBaoHiemTaiSan | null>(null);
    const watchViTri = Form.useWatch("vi_tri", formNhapDoiTuongBaoHiemTaiSan);
    const watchTinhThanh = Form.useWatch("tinh_thanh", formNhapDoiTuongBaoHiemTaiSan);
    // Modal Import Excel ref
    const modalImportExcelRef = useRef<IModalImportExcelRef>(null);
    // useEffect(() => {
    //   timKiemPhanTrangDoiTuongBaoHiemTaiSan({...searchParams, trang: 1, so_dong: PAGE_SIZE});
    // }, []);
    //watch
    // const watchHangXe = Form.useWatch("hang_xe", formNhapDoiTuongBaoHiemTaiSan);
    // const watchBienXe = Form.useWatch("bien_xe", formNhapDoiTuongBaoHiemTaiSan);

    // Hàm dùng chung để chọn đối tượng bảo hiểm tài sản
    const handleSelectDoiTuongBaoHiemTaiSan = useCallback(
      async (record: any) => {
        if (!record.so_id || (record.key && record.key.toString().includes("empty"))) return;
        const response = await layChiTietDoiTuongBaoHiemTaiSan(record as ReactQuery.IChiTietDoiTuongBaoHiemTaiSanParams);
        if (response) initFormFieldsDoiTuong(formNhapDoiTuongBaoHiemTaiSan, response);
        setDoiTuongTaiSanSelected(response || null);
      },
      [layChiTietDoiTuongBaoHiemTaiSan, formNhapDoiTuongBaoHiemTaiSan],
    );
    const handleChangeTinhThanh = useCallback(
      (value: string) => {
        formNhapDoiTuongBaoHiemTaiSan.setFieldValue("phuong_xa", undefined);
        getListPhuongXa({ma_tinh: value});
      },

      [getListPhuongXa],
    );
    const handleChangeViTri = useCallback(
      (value: string) => {
        formNhapDoiTuongBaoHiemTaiSan.setFieldValue("tinh_thanh", undefined);
        formNhapDoiTuongBaoHiemTaiSan.setFieldValue("phuong_xa", undefined);
      },

      [ formNhapDoiTuongBaoHiemTaiSan],
    );
    //DATA TABLE ĐỐI TƯỢNG BB
    const dataTableListDoiTuongBaoHiemTaiSan = useMemo<Array<TableDoiTuongColumnDataType>>(() => {
      try {
        const tableData = danhSachDoiTuongBaoHiemTaiSan.map((item: any, index: number) => ({
          ...item,

          key: index.toString(),
        }));
        //nếu có dữ liệu thì chọn đối tượng đầu tiên trong mảng
        // if (tableData.length > 0) handleSelectDoiTuongBaoHiemTaiSan(tableData[0]);
        // Add empty rows if data is less than 10 rows
        const arrEmptyRow: Array<TableDoiTuongColumnDataType> = fillRowTableEmpty(tableData.length, PAGE_SIZE);
        return [...tableData, ...arrEmptyRow];
      } catch (error) {
        console.log("dataTableListPhongBan error", error);
        return [];
      }
    }, [danhSachDoiTuongBaoHiemTaiSan]);
    // useEffect(() => {
    //   timKiemPhanTrangDoiTuongBaoHiemTaiSan();
    // }, []);
    //hàm tìm kiếm phân trang
    const handleSearchAndPagination = useCallback(
      (values?: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams & Partial<ReactQuery.IPhanTrang>, pageArg?: number) => {
        const so_id = chiTietHopDongBaoHiemTaiSan?.so_id;
        // if là bấm tìm kiếm else là onchange page
        if (values) {
          const cleanedValues = {
            ...values,
            nd_tim: values.nd_tim ?? "",
            dong_tai: values.dong_tai ?? "",
            so_id,
          };
          setSearchParams(cleanedValues);
          setPage(1);
          timKiemPhanTrangDoiTuongBaoHiemTaiSan({...cleanedValues, trang: 1, so_dong: PAGE_SIZE});
        } else {
          const page = pageArg || 1;
          setPage(page);
          timKiemPhanTrangDoiTuongBaoHiemTaiSan({
            ...searchParams,
            so_id,
            trang: page,
            so_dong: PAGE_SIZE,
          });
        }
      },
      [chiTietHopDongBaoHiemTaiSan, searchParams],
    );

    // Handle export Excel using useAsyncAction //Tạo 1 biến isSubmiting riêng cho từng action
    const [handleExportExcel, isExportingExcel] = useAsyncAction(async () => {
      const so_id = chiTietHopDongBaoHiemTaiSan?.so_id;
      if (!so_id) {
        message.error("Không có thông tin hợp đồng để xuất Excel!");
        return;
      }

      try {
        // await exportExcel({so_id});
      } catch (error) {
        console.error("Export Excel error:", error);
        message.error("Có lỗi xảy ra khi xuất Excel!");
        throw error; // Re-throw để useAsyncAction có thể handle
      }
    });

    // Handle import Excel
    const handleImportExcel = useCallback(() => {
      modalImportExcelRef.current?.openFilePicker();
    }, []);

    // Handle confirmed data from Excel import
    const handleExcelDataConfirm = useCallback((data: IExcelRowData[]) => {
      console.log("Excel data confirmed:", data);
      message.success(`Đã import thành công ${data.length} dòng dữ liệu từ Excel!`);

      // TODO: Process the imported data
      // You can:
      // 1. Add the data to the current table
      // 2. Send to server for batch processing
      // 3. Show confirmation dialog
      // 4. Update the form with imported data

      // Example: Log sample data for debugging
      if (data.length > 0) {
        console.log("Sample imported row:", data[0]);
      }

      // Refresh the table data after import
      handleSearchAndPagination();
    }, []);

    // Handle cancel import
    const handleExcelImportCancel = useCallback(() => {
      console.log("Excel import cancelled");
    }, []);

    // RENDER
    const renderFormInputColum = (props?: any, span = 4) => {
      return (
        <Col span={span}>
          <FormInput {...props} />
        </Col>
      );
    };

    //render header table đối tượng
    const renderHeaderTableDoiTuongBaoHiemTaiSan = () => {
      return (
        <div>
          <Form form={formTimKiemPhanTrangDoiTuongBaoHiemTaiSan} layout="vertical" className="[&_.ant-form-item]:mb-0" onFinish={handleSearchAndPagination}>
            <Row gutter={8} className="items-end">
              {renderFormInputColum({...nd_tim}, 24)}
              {/* <Col>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full"></Button>
              </Form.Item>
            </Col>
            <Col>
              <Form.Item>
                <Button
                  className="w-full"
                  type="primary"
                  icon={<PlusCircleOutlined />}
                  onClick={() => {
                    formNhapDoiTuongBaoHiemTaiSan.resetFields();
                    setDoiTuongXeSelected(null);
                  }}
                  loading={loading}></Button>
              </Form.Item>
            </Col> */}
            </Row>
          </Form>
        </div>
      );
    };

    // Helper function để render icon theo giá trị sdbs
    const iConSstyle = {marginRight: "8px", fontSize: "15px", fontWeight: "bold"};
    const renderSdbsIcon = (sdbs: string) => {
      switch (sdbs) {
        case "N": // Đối tượng mới
          return <PlusOutlined style={{...iConSstyle, color: "#52c41a"}} title="Đối tượng mới" />;
        case "S": // Đối tượng sửa đổi
          return <WarningOutlined style={{...iConSstyle, color: "orange"}} title="Đối tượng sửa đổi" />;
        case "K": // Đối tượng không sửa đổi
          return <InfoCircleOutlined style={{...iConSstyle, color: "#8c8c8c"}} title="Đối tượng không sửa đổi" />;
        case "H": // Đối tượng kết thúc hiệu lực
          return <CloseOutlined style={{...iConSstyle, color: "#ff4d4f"}} title="Đối tượng kết thúc hiệu lực" />;
        default:
          return null;
      }
    };
    //render bảng danh sách đối tượng XE
    const renderTableDanhSachDoiTuongXe = () => {
      return (
        <div>
          <Table<TableDoiTuongColumnDataType>
            className="custom-table-title-padding mt-5"
            dataSource={dataTableListDoiTuongBaoHiemTaiSan} //mảng dữ liệu record được hiển thị
            columns={
              (tableDoiTuongColumn || []).map(item => {
                // Override render function cho column bien_xe để thêm icon
                if ("dataIndex" in item && item.dataIndex === "ten") {
                  return {
                    ...item,
                    render: (text: any, record: any) => {
                      if (record.key && record.key.toString().includes("empty")) return "\u00A0";

                      const icon = renderSdbsIcon(record.sdbs);
                      const bienXeText = text !== undefined ? text : "";

                      return (
                        <span className="flex items-center">
                          {icon}
                          {bienXeText}
                        </span>
                      );
                    },
                  };
                }
                return {...item};
              }) || []
            } //định nghĩa cột của table
            loading={loading} //hiển thị loading khi đang gọi API để loading data
            sticky
            rowClassName={record => (record.so_id_dt && record.so_id_dt === doiTuongTaiSanSelected?.gcn?.so_id_dt ? "table-row-active" : "")} // ✅ Set class cho dòng được chọn
            pagination={{
              ...defaultPaginationTableProps,
              total: tongSoDongDoiTuongBaoHiemTaiSan,
              pageSize: PAGE_SIZE,
              current: page, //set current page
              onChange: (page, pageSize) => {
                handleSearchAndPagination(undefined, page);
              },
              locale: {
                jump_to: "Tới trang",
                page: "",
              },
            }}
            showHeader={true}
            title={renderHeaderTableDoiTuongBaoHiemTaiSan}
            bordered //set border cả table
            onRow={record => {
              return {
                style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
                onClick: async () => {
                  console.log("record", record);
                  await handleSelectDoiTuongBaoHiemTaiSan(record);
                }, // click row
              };
            }}
            summary={() => (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  {/* Cột đầu tiên: merge các cột trước cột phí bảo hiểm */}
                  <Table.Summary.Cell index={0} colSpan={1} className="!p-[8px]">
                    <div className="text-center font-medium">Tổng phí bảo hiểm</div>
                  </Table.Summary.Cell>
                  {/* Cột phí bảo hiểm: hiển thị tổng */}
                  <Table.Summary.Cell index={1} align="right" className="!p-[8px]">
                    <div className="text-right font-medium">{formatCurrencyUS(tongPhiBaoHiemTaiSanFromAPI)}</div>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            )}
          />
        </div>
      );
    };

    //render form nhập thông tin đối tượng
    const renderForm = () => (
      <Form form={formNhapDoiTuongBaoHiemTaiSan} layout="vertical" className="ml-2 mt-4">
        <Row gutter={16}>
          {renderFormInputColum({...ten}, 5)}

          {renderFormInputColum({...gcn}, 6)}
          {renderFormInputColum({...vi_tri, options: VI_TRI, onchange: handleChangeViTri})}
          {renderFormInputColum({...tinh_thanh, options: listTinhThanh, onChange: handleChangeTinhThanh, disabled: watchViTri === "K" ? true : false, rules: watchViTri === "K" ? [] : [ruleRequired]})}
          {renderFormInputColum({...phuong_xa, options: listPhuongXa, disabled: watchViTri === "K" ? true : false, rules: watchViTri === "K" ? [] : [ruleRequired]})}
          {renderFormInputColum({...dchi, disabled: watchViTri === "K" ? true : false, rules: watchViTri === "K" ? [] : [ruleRequired]}, 6)}
        </Row>

        <Row gutter={16}>
          {renderFormInputColum(
            {
              ...nhom_dt,
              options: listNhomDoiTuong,
            },
            6,
          )}
          {renderFormInputColum({...gia_tri})}
          {renderFormInputColum({...latitude})}
          {renderFormInputColum({...longitude})}
          {renderFormInputColum({...vip, options: listHDVipSelect})}
        </Row>

        <Row gutter={16}>
          {renderFormInputColum({
            ...ngay_cap,
            onChange: (date: Dayjs) => {
              formNhapDoiTuongBaoHiemTaiSan.setFields([
                {name: "ngay_hl", value: date},
                {name: "ngay_kt", value: date.add(1, "y")},
              ]);
            },
          })}
          {renderFormInputColum({
            ...gio_hl,
            onChange: (date: Dayjs) => {
              formNhapDoiTuongBaoHiemTaiSan.setFields([{name: "gio_kt", value: date}]);
            },
          })}
          {renderFormInputColum({
            ...ngay_hl,
            onChange: (date: Dayjs) => {
              formNhapDoiTuongBaoHiemTaiSan.setFields([{name: "ngay_kt", value: date.add(1, "y")}]);
            },
          })}
          {renderFormInputColum({...gio_kt})}
          {renderFormInputColum({...ngay_kt})}
          {/* các input ẩn */}
          {renderFormInputColum({...so_id_dt})}
          {renderFormInputColum({...so_id})}
          {renderFormInputColum({...dk})}
          {renderFormInputColum({...dkbs})}
        </Row>
      </Form>
    );
    //render tab thông tin đối tượng bảo hiểm
    const renderTabThongTinDoiTuong = () => {
      return (
        <div className="ml-2">
          <Tabs
            animated={true}
            size="small"
            defaultActiveKey="1"
            tabBarStyle={{marginBottom: "8px"}}
            items={[
              {
                key: "1",
                label: "Thông tin đối tượng bảo hiểm",
                children: renderForm(),
              },
              {
                key: "2",
                label: "Danh sách tài sản",
                // children: (
                //   <RenderLoaiHinhNghiepVuTable
                //     chiTietHopDongBaoHiem={chiTietHopDongBaoHiemTaiSan}
                //     chiTietDoiTuongBaoHiemTaiSan={doiTuongXeSelected || ({} as CommonExecute.Execute.IDoiTuongBaoHiemTaiSanCoGioi)}
                //     onDataChange={data => {
                //       formNhapDoiTuongBaoHiemTaiSan.setFieldValue("dk", data);
                //     }}
                //   />
                // ),
              },
            ]}
          />
        </div>
      );
    };
    //render tabs sản phẩm bảo hiểm
    // const renderTabs = () => {
    //   return (
    //     <div className="ml-2">
    //       <Tabs
    //         animated={true}
    //         size="small"
    //         defaultActiveKey="1"
    //         tabBarStyle={{marginBottom: "8px"}}
    //         items={[
    //           {
    //             key: "1",
    //             label: "Loại hình nghiệp vụ",
    //             children: (
    //               <RenderLoaiHinhNghiepVuTable
    //                 chiTietHopDongBaoHiem={chiTietHopDongBaoHiemTaiSan}
    //                 chiTietDoiTuongBaoHiemTaiSan={doiTuongXeSelected || ({} as CommonExecute.Execute.IDoiTuongBaoHiemTaiSanCoGioi)}
    //                 onDataChange={data => {
    //                   formNhapDoiTuongBaoHiemTaiSan.setFieldValue("dk", data);
    //                 }}
    //               />
    //             ),
    //           },
    //           {
    //             key: "2",
    //             label: "Điều khoản bổ sung",
    //             children: (
    //               <RenderDieuKhoanBoSungTable
    //                 chiTietHopDongBaoHiem={chiTietHopDongBaoHiemTaiSan}
    //                 chiTietDoiTuongBaoHiemTaiSan={doiTuongXeSelected || ({} as CommonExecute.Execute.IDoiTuongBaoHiemTaiSanCoGioi)}
    //                 onDataChange={data => {
    //                   formNhapDoiTuongBaoHiemTaiSan.setFieldValue("dkbs", data);
    //                 }}
    //               />
    //             ),
    //           },
    //         ]}
    //         // onChange={onChange}
    //       />
    //     </div>
    //   );
    // };

    return (
      <>
        <Row>
          <Col span={6}>
            {/* Table danh sách đối tượng */}
            {renderTableDanhSachDoiTuongXe()}
            <Row gutter={8} className="mt-5">
              {/* <Col>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} className="w-full"></Button>
              </Form.Item>
            </Col> */}
              <Col>
                <Button type="dashed" icon={<UploadOutlined />} className="w-full" onClick={handleImportExcel}>
                  Import
                </Button>
              </Col>
              <Col>
                <Button type="primary" loading={isExportingExcel} icon={<DownloadOutlined />} className="w-full" onClick={handleExportExcel}>
                  Export
                </Button>
              </Col>
              <Col>
                <Button
                  className="w-full"
                  type="primary"
                  icon={<PlusCircleOutlined />}
                  onClick={() => {
                    formNhapDoiTuongBaoHiemTaiSan.resetFields();
                    setDoiTuongTaiSanSelected(null);
                  }}
                  loading={loading}>
                  Thêm mới
                </Button>
              </Col>
            </Row>
          </Col>

          <Col span={18}>
            {/* Form nhập thông tin đối tượng */}
            {renderTabThongTinDoiTuong()}
            {/* Tabs loai hình nghiệp vụ, điều khoản bổ sung */}
            {/* {renderTabs()} */}
          </Col>
        </Row>

        {/* Modal Import Excel */}
        <ModalImportExcel ref={modalImportExcelRef} onDataConfirm={handleExcelDataConfirm} onCancel={handleExcelImportCancel} />
      </>
    );
  },
);

ThongTinDoiTuongBaoHiemTaiSanStepComponent.displayName = "ThongTinDoiTuongBaoHiemTaiSanStepComponent";
export const ThongTinDoiTuongBaoHiemTaiSanStep = memo(ThongTinDoiTuongBaoHiemTaiSanStepComponent, isEqual);
