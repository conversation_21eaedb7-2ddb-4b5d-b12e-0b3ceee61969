import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface BaoHiemXeCoGioiContextProps {
  danhSachHopDongBaoHiemXeCoGioi: Array<CommonExecute.Execute.IHopDongXe>;
  danhSachKhachHang: Array<CommonExecute.Execute.IKhachHang>;
  loading: boolean;
  layDanhSachHopDongBaoHiemXePhanTrang: (params: ReactQuery.ILayDanhSachHopDongXePhanTrangParams) => void;
  timKiemPhanTrangKhachHang: (params: ReactQuery.ILayDanhSachKhachHangPhanTrangParams) => void;
  tongSoDong: number;
  layChiTietHopDongBaoHiemXe: (params: ReactQuery.IChiTietHopDongXeParams) => Promise<CommonExecute.Execute.IHopDongXe | null>;
  defaultFormValue: object;
  getListCanBoQuanLy: (params: ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams) => Promise<{data: Array<CommonExecute.Execute.IDoiTacNhanVien>; tong_so_dong: number}>;
  getListDaiLyKhaiThac: (params: ReactQuery.ILietKeDanhSachDaiLyParams) => Promise<{data: Array<CommonExecute.Execute.IDanhMucDaiLy>}>;
  searchKhachHang: (params: ReactQuery.ILayDanhSachKhachHangPhanTrangParams) => Promise<{tong_so_dong: number; data: Array<CommonExecute.Execute.IKhachHang>}>;
  timKiemPhanTrangDaiLy: (params: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams) => void;
  danhSachDaiLy: Array<CommonExecute.Execute.IDanhMucDaiLy>;
  updateHopDongXe: (params: ReactQuery.IUpdateHopDongParams) => Promise<{
    success: boolean;
    isNewContract: boolean;
    contractInfo?: {
      so_id: number;
      so_hd: string;
    };
  }>;
  updateDoiTuongBaoHiemXe: (params: ReactQuery.IFormCapNhatDoiTuongBaoHiemXeCoGioiParams) => Promise<boolean>;
  timKiemPhanTrangDoiTuongBaoHiemXe: (params: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams) => void;
  danhSachDoiTuongBaoHiemXe: Array<CommonExecute.Execute.IHopDongXe>;
  tongSoDongDoiTuongBaoHiemXe: number;
  tongPhiBaoHiemXeFromAPI: number;
  tongSoDongCanBoQuanLy: number;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listPhuongThucKhaiThac: Array<CommonExecute.Execute.IChiTietPhuongThucKhaiThac>;
  listSanPham: Array<CommonExecute.Execute.ISanPham>;
  listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>;
  listPhongBan: Array<CommonExecute.Execute.IPhongBan>;
  listDonViBoiThuong: Array<CommonExecute.Execute.INhaBaoHiemTPA>;
  listChuongTrinhBaoHiem: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>;
  listCanBo: Array<CommonExecute.Execute.IDoiTacNhanVien>;
  listLoaiXe: Array<CommonExecute.Execute.ILoaiXe>;
  listHangXe: Array<CommonExecute.Execute.IHangXe>;
  listHieuXe: Array<CommonExecute.Execute.IHieuXe>;
  layChiTietDoiTuongBaoHiemXe: (params: ReactQuery.IChiTietDoiTuongBaoHiemXeParams) => Promise<CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi | null>;
  listLoaiHinhNghiepVuXeCoGioi: Array<CommonExecute.Execute.ILoaiHinhNghiepVuXeCoGioi>;
  listDieuKhoanBoSungXeCoGioi: Array<CommonExecute.Execute.ILoaiHinhNghiepVuXeCoGioi>;
  layThongTinThanhToanCuaHopDongBaoHiemXe: (params: ReactQuery.ILayThongTinThanhToanCuaHopDongBaoHiemParams) => void;
  listThongTinThanhToanHopDongXe: Array<CommonExecute.Execute.IThongTinThanhToanCuaHopDong>;
  chiTietHopDongBaoHiemXe: CommonExecute.Execute.IHopDongXe;
  updateKyThanhToan: (params: ReactQuery.IUpdateKyThanhToanParams) => Promise<boolean>;
  layChiTietKyThanhToan: (params: ReactQuery.IChiTietKyThanhToanParams) => Promise<CommonExecute.Execute.IKyThanhToan | null>;
  tongSoDongDataKhachHang: number;
  tongSoDongDataLDaiLy: number;
  layDanhSachCauHinhDongCuaHopDongBaoHiemXe: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise<any>;
  listDongBaoHiemHopDongXe: Array<CommonExecute.Execute.IDongBaoHiem>;
  updateCauHinhDongBaoHiem: (params: ReactQuery.IUpdateCauHinhDongBHParams) => Promise<boolean>;
  listDonViDongTai: Array<CommonExecute.Execute.IDonViDongTai>;
  layChiTietThongTinCauHinhDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<CommonExecute.Execute.IDongBaoHiem | null>;
  xoaThongTinDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<boolean>;
  layDanhSachCauHinhTaiBHCuaHopDongBaoHiemXe: (params: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams) => Promise<any>;
  listTaiBaoHiemHopDongXe: Array<CommonExecute.Execute.ITaiBaoHiem>;
  updateCauHinhTaiBaoHiem: (params: ReactQuery.IUpdateCauHinhTaiBHParams) => Promise<boolean>;
  xoaThongTinTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<boolean>;
  layChiTietThongTinCauHinhTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<CommonExecute.Execute.ITaiBaoHiem | null>;
  updateDoiTuongApDungTyLeDongBH: (params: ReactQuery.IUpdateDoiTuongApDungDongBHParams) => Promise<boolean>;
  updateDoiTuongApDungTaiBH: (params: ReactQuery.IUpdateDoiTuongApDungTaiBHParams) => Promise<boolean>;
  lietKeDanhSachCacDoiTuongDaDuocApDungDongBH: (params: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => Promise<any>;
  lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH: (params: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => Promise<any>;
  getDanhSachNguoiDuyetHopDong: (params: ReactQuery.ILietKeDanhSachNguoiDuyetParams) => Promise<any>;
  danhSachNguoiDuyetHopDong: Array<CommonExecute.Execute.INguoiPheDuyetHopDongXe>;
  huyHopDongXe: () => Promise<boolean>;
  goHuyHopDongXe: () => Promise<boolean>;
  trinhPheDuyetHopDong: (params: CommonExecute.Execute.ITrinhPheDuyetHopDong) => Promise<boolean>;
  huyTrinhPheDuyetHopDong: () => Promise<boolean>;
  getDanhSachFileThumbnailTheoDoiTuong: (params: ReactQuery.IGetFileThumbnailParams) => Promise<any>;
  uploadFileTheoDoiTuong: (params: ReactQuery.IUploadFileTheoDoiTuongXeParams) => Promise<boolean>;
  deleteFileTheoDoiTuong: (params: ReactQuery.IUploadFileTheoDoiTuongXeParams) => Promise<boolean>;
  phanLoaiFileTheoHangMucXe: (params: ReactQuery.IPhanLoaiFileTheoHangMucXeParams) => Promise<boolean>;
  getListHangMucXeTheoNhom: (params: ReactQuery.ILietKeHangMucXeTheoNhomParams) => Promise<any>;
  listHangMucXeTheoNhom: Array<CommonExecute.Execute.IHangMucXe>;
  exportPdfHopDong: (params: ReactQuery.IExportPDFHopDongParams) => Promise<string | null>;
  exportExcel: (params: ReactQuery.IExportExcelParams) => Promise<string | null>;
  getListMucDoTonThatXe: () => Promise<any>;
  listMucDoTonThatXe: Array<CommonExecute.Execute.IMucDoTonThatXe>;
  layDanhSachHangMucTonThatTheoDoiTuongXe: (params: ReactQuery.IChiTietDoiTuongBaoHiemXeParams) => Promise<any>;
  timKiemPhanTrangHangMucTonThat: (params: ReactQuery.ITimKiemPhanTrangHangMucTonThatParams) => Promise<any>;
  danhSachHangMucTonThat: Array<any>;
  tongSoDongHangMucTonThat: number;
  luuHangMucTonThat: (params: ReactQuery.ILuuHangMucTonThatParams) => Promise<boolean>;
  luuDanhGiaTonThatXe: (params: ReactQuery.ILuuDanhGiaTonThatParams) => Promise<boolean>;
  chiTietDoiTuongBaoHiemXe: CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi;
  dataHangMucTonThatTheoDoiTuong: CommonExecute.Execute.IHangMucXe[];
  taoHopDongSuaDoiBoSung: (params: ReactQuery.ITaoHopDongSuaDoiBoSungParams) => Promise<{so_id: number; so_hd: string} | null>;
  resetChiTietHopDongBaoHiemXe: () => void;
}
