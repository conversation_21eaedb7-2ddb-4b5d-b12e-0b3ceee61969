import {IFormInput} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};

export interface IFormChiTietNhomDoiTuongFieldsConfig {
  ma: IFormInput;
  ten: IFormInput;
  ma_cha: IFormInput;
  ma_dau: IFormInput;
  mo_ta: IFormInput;
  stt: IFormInput;
  trang_thai: IFormInput;
  trang_thai_ten: IFormInput;
  ma_ct: IFormInput;
}
export const FormChiTietNhomDoiTuong: IFormChiTietNhomDoiTuongFieldsConfig = {
  ma: {
    component: "input",
    label: "Mã nhóm đối tượng",
    name: "ma",
    placeholder: "Mã nhóm đối tượng",
    rules: [ruleRequired],
  },
  ten: {
    component: "input",
    label: "Tên nhóm đối tượng",
    name: "ten",
    placeholder: "Tên nhóm đối tượng",
    rules: [ruleRequired],
  },

  stt: {
    component: "input",
    label: "Thứ tự hiển thị",
    name: "stt",
    placeholder: "Thứ tự hiển thị",
    rules: [ruleRequired],
  },
  ma_cha: {
    component: "input",
    label: "Mã nhóm đối tượng cha",
    name: "ma_cha",
    placeholder: "Mã nhóm đối tượng cha",
  },
  ma_dau: {
    component: "input",
    label: "Mã nhóm đối tượng đầu",
    name: "ma_dau",
    placeholder: "Mã nhóm đối tượng đầu",
  },
  mo_ta: {
    component: "textarea",
    label: "Mô tả",
    name: "mo_ta",
    placeholder: "Mô tả",
  },
  trang_thai: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai",
    placeholder: "Chọn trạng thái",
    rules: [ruleRequired],
  },
  trang_thai_ten: {
    component: "select",
    label: "Trạng thái",
    name: "trang_thai_ten",
    placeholder: "Chọn trạng thái",
  },
  ma_ct: {
    component: "select",
    label: "Nhóm đối tượng cấp trên",
    name: "ma_ct",
    placeholder: "Chọn nhóm đối tượng cấp trên",
    showSearch: false,
  },
};

export interface ChiTietNhomDoiTuongProps {}
//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalChiTietNhomDoiTuongRef {
  open: (data?: CommonExecute.Execute.INhomDoiTuong) => void;
  close: () => void;
}
export interface IModalThemNhomDoiTuongChaRef {
  open: (data?: CommonExecute.Execute.INhomDoiTuong) => void;
  close: () => void;
}

//Danh mục nhóm đối tượng cha
export interface TableNhomDoiTuongChaDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  ma_cha?: string;
  ma_dau?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const NhomDoiTuongChaColumns: TableProps<TableNhomDoiTuongChaDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 200, align: "left", ...defaultTableColumnsProps},
  {title: "Mã cha", dataIndex: "ma_cha", key: "ma_cha", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Mã đầu", dataIndex: "ma_dau", key: "ma_dau", width: 120, align: "center", ...defaultTableColumnsProps},
];
// export type DataIndex = keyof TableNhomDoiTuongDataType;
export type DataIndexCha = keyof TableNhomDoiTuongChaDataType;
//keyof: return ra key của inteface TableKhachHangColumnDataType;
export type TableNhomDoiTuongChaColumnDataIndex = keyof TableNhomDoiTuongChaDataType;
export interface NhomDoiTuongChaProps {
  onSelectNhomDoiTuongCha: (ma_cha: CommonExecute.Execute.INhomDoiTuong | null) => void;
  chiTietNhomDoiTuong: CommonExecute.Execute.INhomDoiTuong | null;
}
export interface INhomDoiTuongSelected {
  ma?: string;
  ten?: string;
}
//Nhóm đối tượng chi tiết
export interface TableDoiTuongCTDataType {
  key: string;
  ma_thuoc_tinh: string;
  ten_thuoc_tinh: string;
  kieu_dl: string;
  // hanh_dong?: () => JSX.Element | null; // Định nghĩa hanh_dong là hàm trả về JSX.Element;
}
export const doiTuongCTColumns: TableProps<TableDoiTuongCTDataType>["columns"] = [
  // {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center", ...defaultTableColumnsProps},
  {title: "Mã thuộc tính", dataIndex: "ma_thuoc_tinh", key: "ma_thuoc_tinh", width: 120, align: "center", ...defaultTableColumnsProps},
  {title: "Tên thuộc tính", dataIndex: "ten_thuoc_tinh", key: "ten_thuoc_tinh", width: 200, align: "left", ...defaultTableColumnsProps},
  {title: "Kiểu dữ liệu", dataIndex: "kieu_dl", key: "kieu_dl", width: 120, align: "center", ...defaultTableColumnsProps},
];
export type DataIndexDoiTuongCT = keyof TableDoiTuongCTDataType;
//KIEU_DU_LIEU DATE, NUMBER, TEXT
export const KIEU_DU_LIEU = [
  {ten: "DATE", ma: "DATE"},
  {ten: "NUMBER", ma: "NUMBER"},
  {ten: "TEXT", ma: "TEXT"},
];
