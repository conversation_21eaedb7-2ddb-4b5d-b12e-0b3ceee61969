import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps, ruleInputMessage} from "@src/hooks";
import {formatCurrencyUS} from "@src/utils";
import {StepProps, TableProps} from "antd";
import {FormInstance} from "antd/lib";
import {Dispatch, RefObject, SetStateAction} from "react";
import {NumericFormat} from "react-number-format";
//Hàm ép kiểu dùng dung
export const parseNumber = (value: any): number => {
  // Xử lý các trường hợp null, undefined, empty string
  if (value == null || value === "") return 0;

  // Nếu đã là number, kiểm tra NaN và trả về
  if (typeof value === "number") {
    return Number.isNaN(value) ? 0 : value;
  }

  // Nếu là string, làm sạch và parse
  if (typeof value === "string") {
    // Loại bỏ khoảng trắng đầu cuối
    const trimmed = value.trim();
    if (trimmed === "") return 0;

    // Loại bỏ dấu phẩy phân cách hàng nghìn và ký tự không phải số, dấu chấm, dấu trừ
    const cleaned = trimmed.replace(/[,]/g, "").replace(/[^0-9.-]/g, "");

    // Kiểm tra format hợp lệ (chỉ có 1 dấu chấm và 1 dấu trừ ở đầu)
    if (!/^-?\d*\.?\d*$/.test(cleaned)) return 0;

    const parsed = Number(cleaned);
    return Number.isNaN(parsed) ? 0 : parsed;
  }

  // Các trường hợp khác (boolean, object, array, etc.)
  return 0;
};

/* HỢP ĐỒNG CON NGƯỜI */
export const STEP_THEM_HOP_DONG: StepProps[] = [
  {
    title: "Thông tin hợp đồng",
  },
  {
    title: "Nhập đối tượng bảo hiểm",
  },
  {
    title: "Hình ảnh / Hồ sơ / Tài liệu",
  },
  {
    title: "Thông tin cấu hình",
  },
  {
    title: "Phê duyệt hợp đồng",
  },
];

export const messageAlertByStep = {
  0: "Bạn có chắc chắn muốn lưu thông tin hợp đồng?",
  1: "Bạn có chắc chắn muốn lưu thông tin đối tượng bảo hiểm?",
  2: "Bạn có chắc chắn muốn lưu thông tin thanh toán?",
  3: "Bạn có chắc chắn muốn lưu thông tin cấu hình?",
  4: "Bạn có chắc chắn muốn lưu thông tin phê duyệt?",
};
/** MODAL TÌM KHÁCH HÀNG */
//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableKhachHangColumnDataType {
  key: string;
  sott?: number; //CỘT 1
  ten?: string; //CỘT 2
  ten_loai_kh?: string;
  ma?: string;
  dchi?: string;
  mst?: string;
  dthoai?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableKhachHangColumn: TableProps<TableKhachHangColumnDataType>["columns"] = [
  {title: "STT", dataIndex: "sott", key: "sott", align: "center", width: colWidthByKey.sott, ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 400, ellipsis: true, ...defaultTableColumnsProps},
  {title: "Khách hàng", dataIndex: "ten_loai_kh", key: "ten_loai_kh", align: "center", width: 120, ...defaultTableColumnsProps},
  {title: "Mã KH", dataIndex: "ma", key: "ma", align: "center", width: 80, ...defaultTableColumnsProps},
  {title: "Địa chỉ", dataIndex: "dchi", key: "dchi", width: 400, ...defaultTableColumnsProps},
  {title: "MST", dataIndex: "mst", key: "mst", align: "center", width: 150, ...defaultTableColumnsProps},
  {title: "Điện thoại", dataIndex: "dthoai", key: "dthoai", width: 120, ...defaultTableColumnsProps},
  {title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 150, align: "center", ...defaultTableColumnsProps},
];

//keyof: return ra key của inteface TableKhachHangColumnDataType;
export type TableKhachHangColumnDataIndex = keyof TableKhachHangColumnDataType;

//radio trong table
export const radioItemTrangThaiKhachHangTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

export const radioItemLoaiKhachHangTable = [
  {value: "Cá nhân", text: "Cá nhân"},
  {value: "Tổ chức", text: "Tổ chức"},
];
export interface ModalTimKhachHangProps {
  maDoiTacSelected: string;
  maChiNhanhSelected: string;
  listChiNhanhSelect: Array<CommonExecute.Execute.IChiNhanh>;
  onSelectKhachHang: (ma_kh: CommonExecute.Execute.IKhachHang | null) => void;
}
// FORM TÌM KIẾM KHÁCH HÀNG
export interface IFormTimKiemKhachHangFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma_chi_nhanh_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  loai_kh: IFormInput;
  dthoai: IFormInput;
  cmt: IFormInput;
  mst: IFormInput;
  nd_tim: IFormInput;
}

export const defaultFilterKhachHangParams: ReactQuery.ILayDanhSachKhachHangPhanTrangParams = {
  ma_doi_tac_ql: "",
  ma_chi_nhanh_ql: "",
  loai_kh: "",
  ma: "",
  ten: "",
  dthoai: "",
  cmt: "",
  mst: "",
  nd_tim: "",
  trang: 1,
  so_dong: 10,
};

export const FormTimKiemKhachHang: IFormTimKiemKhachHangFieldsConfig = {
  ma_doi_tac_ql: {component: "select", label: "Đối tác", name: "ma_doi_tac_ql", rules: [ruleInputMessage.required], autoClearSearchValue: true, disabled: true},
  ma_chi_nhanh_ql: {component: "select", label: "Chi nhánh", name: "ma_chi_nhanh_ql", autoClearSearchValue: true, allowClear: true, disabled: true},
  ma: {component: "input", label: "Mã", name: "ma", placeholder: "Mã khách hàng"},
  ten: {component: "input", label: "Tên", name: "ten", placeholder: "Tên khách hàng"},
  loai_kh: {component: "select", label: "Loại khách hàng", name: "loai_kh", placeholder: "Chọn loại khách hàng"},
  dthoai: {component: "input", name: "dthoai", placeholder: "Nhập số điện thoại"},
  cmt: {component: "input", name: "cccd", placeholder: "Nhập CCCD/CMT"},
  mst: {component: "input", name: "mst", placeholder: "Nhập mã số thuế"},
  nd_tim: {component: "input", name: "nd_tim", placeholder: "Nhập nội dung tìm"},
};

export const radioItemLoaiKhachHangSelect: Array<{ma: string; ten: string}> = [
  {ten: "Tổ chức", ma: "T"},
  {ten: "Cá nhân", ma: "C"},
];
//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalTimKhachHangRef {
  open: (khachHangSelected?: {ma: string; ten: string}) => void;
  close: () => void;
}

/* MODAL TẠO MỚI HỢP ĐỒNG */
export interface IModalThemHopDongRef {
  open: (data?: CommonExecute.Execute.IHopDongConNguoi) => void;
  close: () => void;
}

export interface ModalThemHopDongProps {}

/* THÊM HỢP ĐỒNG BƯỚC 1 */
export interface IFormTaoMoiHopDongFieldsConfig {
  ma_doi_tac_ql: IFormInput; // Đối tác cấp đơn
  ma_chi_nhanh_ql: IFormInput; //Chi nhánh cấp đơn
  phong_ql: IFormInput; //phòng ban
  ma_kh: IFormInput; //khách hàng
  kieu_hd: IFormInput; //kiểu hợp đồng
  so_hd_g: IFormInput; //Số HD gốc
  ma_cb_ql: IFormInput; //cán bộ quản lý
  ngay_cap: IFormInput; //ngày cấp
  gio_hl: IFormInput; //giờ hiệu lực
  ngay_hl: IFormInput; //ngày hiệu lực
  gio_kt: IFormInput; //giờ kết thúc
  ngay_kt: IFormInput; //ngày kết thúc
  ma_sp: IFormInput; //sản phẩm
  ma_ctbh: IFormInput; //chương trình bảo hiểm
  pt_kt: IFormInput; //phương thức khai thác
  daily_kt: IFormInput; //đại lý khai thác
  ma_nha_tpa: IFormInput; //đơn vị bồi thường - chưa có tham số
  vip: IFormInput; //hợp đồng VIP

  // so_id: IFormInput;
  so_hd: IFormInput;
  // nv: IFormInput; //nghiệp vụ NG
}

export type IFormTaoMoiHopDongFieldsConfigKeys = keyof IFormTaoMoiHopDongFieldsConfig;

export const FormTaoMoiHopDong: IFormTaoMoiHopDongFieldsConfig = {
  ma_doi_tac_ql: {component: "select", label: "Đối tác cấp đơn", name: "ma_doi_tac_ql", placeholder: "Chọn đối tác cấp đơn", rules: [ruleInputMessage.required], allowClear: false},
  ma_chi_nhanh_ql: {component: "select", label: "Chi nhánh cấp đơn", name: "ma_chi_nhanh_ql", placeholder: "Chọn chi nhánh cấp đơn", rules: [ruleInputMessage.required], allowClear: false},
  phong_ql: {component: "select", label: "Phòng ban", name: "phong_ql", placeholder: "Chọn phòng ban", rules: [ruleInputMessage.required], allowClear: false},
  ma_kh: {component: "select", label: "Khách hàng", name: "ma_kh", open: false, placeholder: "Chọn khách hàng", rules: [ruleInputMessage.required], showSearch: false, allowClear: false},
  so_hd: {component: "input", label: "Số hợp đồng", name: "so_hd", placeholder: "Nhập số hợp đồng gốc", rules: [ruleInputMessage.required]},
  kieu_hd: {component: "select", label: "Kiểu hợp đồng", name: "kieu_hd", placeholder: "Chọn kiểu hợp đồng", allowClear: false, rules: [ruleInputMessage.required]},
  so_hd_g: {component: "input", label: "Số hợp đồng gốc", name: "so_hd_g", placeholder: "Nhập số hợp đồng gốc", rules: [ruleInputMessage.required]},
  ma_cb_ql: {
    component: "select",
    label: "Cán bộ quản lý",
    name: "ma_cb_ql",
    placeholder: "Chọn cán bộ quản lý",
    rules: [ruleInputMessage.required],
    filterOption: false, //tắt lọc nội bộ
    open: false, //click vào select sẽ k mở dropdown
    allowClear: false,
    showSearch: false,
  },
  ngay_cap: {component: "date-picker", label: "Ngày cấp", name: "ngay_cap", allowClear: false, rules: [ruleInputMessage.required]},
  gio_hl: {component: "time-picker", label: "Giờ hiệu lực", name: "gio_hl"},
  ngay_hl: {component: "date-picker", label: "Ngày hiệu lực", name: "ngay_hl", allowClear: false, rules: [ruleInputMessage.required]},
  gio_kt: {component: "time-picker", label: "Giờ kết thúc", name: "gio_kt"},
  ngay_kt: {component: "date-picker", label: "Ngày kết thúc", name: "ngay_kt", placeholder: "Chọn ngày kết thúc"},
  ma_sp: {component: "select", label: "Sản phẩm", name: "ma_sp", placeholder: "Chọn sản phẩm", rules: [ruleInputMessage.required], allowClear: false},
  ma_ctbh: {component: "select", label: "Chương trình bảo hiểm", name: "ma_ctbh", placeholder: "Chọn chương trình bảo hiểm", rules: [ruleInputMessage.required], allowClear: false},
  pt_kt: {component: "select", label: "Phương thức khai thác", name: "pt_kt", placeholder: "Chọn phương thức khai thác", rules: [ruleInputMessage.required], allowClear: false},
  daily_kt: {
    component: "select",
    label: "Đại lý khai thác",
    name: "daily_kt",
    placeholder: "Chọn đại lý khai thác",
    rules: [ruleInputMessage.required],
    filterOption: false, //tắt lọc nội bộ open: false, //click vào select sẽ k mở dropdown allowClear: false, showSearch: false,
  },
  ma_nha_tpa: {component: "select", label: "Đơn vị bồi thường", name: "ma_nha_tpa", placeholder: "Chọn đơn vị bồi thường", rules: [ruleInputMessage.required], allowClear: false},
  vip: {component: "select", label: "Hợp đồng VIP", name: "vip", placeholder: "Chọn hợp đồng VIP", rules: [ruleInputMessage.required], allowClear: false},
};

//option select nghiệp vụ
export const listKieuHopDongSelect: Array<{ma: string; ten: string}> = [
  {ma: "G", ten: "Hợp đồng gốc"},
  {ma: "B", ten: "Hợp đồng sửa đổi bố sung"},
  {ma: "T", ten: "Hợp đồng tái tục"},
];

//option select hợp đồng VIP
export const listHDVipSelect: Array<{ma: string; ten: string}> = [
  {ma: "VIP", ten: "VIP"},
  {ma: "K", ten: "Không"},
];

export interface ThemHopDongStep1Props {
  formThongTinHopDong: FormInstance<any>;
  setIsChangeData: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface IThemHopDongStep1Ref {}

/* THÊM HỢP ĐỒNG BƯỚC 2 */
export interface ThemHopDongStep2Props {
  formThongTinNguoiBaoHiem: FormInstance<any>;
  setIsChangeData: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface IThemHopDongStep2Ref {
  getCurrentTab: () => string;
  getListQuyenLoiNguoiDuocBaoHiem: () => CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
  getCheckboxApDungGoiBaohiemQuyenLoi: () => boolean;
  getListQuyenLoiBoSungNguoiDuocBaoHiem: () => CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
  getCheckboxApDungGoiBaohiemQuyenLoiBoSung: () => boolean;
  getDanhSachCauHoiDanhGiaSucKhoe: () => IDanhGiaSucKhoeCauHoi[];
  getDanhSachCauTraLoiDanhGiaSucKhoe: () => IDanhGiaSucKhoeCauTraLoi[];
  getDataForSaveDanhGiaSucKhoe: () => {dgsk: Array<{ma?: string; dap_an?: string}>};
  validateDanhGiaSucKhoe: () => {isValid: boolean; errors: string[]};
  refreshDanhGiaSucKhoe: () => void; // Thêm method để refresh tab đánh giá sức khoẻ
  refreshData: () => void; // Thêm method để làm mới dữ liệu
}

// FORM TẠO MỚI NGƯỜI ĐƯỢC BẢO HIỂM
export interface IFormTaoMoiNguoiDuocBaoHiemFieldsConfig {
  ten: IFormInput; // tên
  ngay_sinh: IFormInput; //ngày sinh
  gioi_tinh: IFormInput; //giới tính
  so_cmt: IFormInput; //số CMT
  dthoai: IFormInput; //điện thoại
  email: IFormInput; // email
  dia_chi: IFormInput; //địa chỉ
  ngay_cap: IFormInput; //ngày cấp
  gcn: IFormInput; //giấy chứng nhận
  vip: IFormInput; //vip
  gio_hl: IFormInput; //giờ hiệu lực
  ngay_hl: IFormInput; //ngày hiệu lực
  gio_kt: IFormInput; //giờ kết thúc
  ngay_kt: IFormInput; //ngày kết thúc
  ma_goi_bh: IFormInput; //nhóm gói bảo hiểm
  cnhanh_ctac: IFormInput; // chi nhánh công tác
  pban_ctac: IFormInput; // phòng công tác
  cvu_ctac: IFormInput; // chức vụ
  cty_ctac: IFormInput;
  ma_nv_ctac: IFormInput;
  email_ctac: IFormInput;
}

export const FormNguoiDuocBaoHiem: IFormTaoMoiNguoiDuocBaoHiemFieldsConfig = {
  ten: {component: "input", label: "Tên người được bảo hiểm", name: "ten", placeholder: "Nhập tên người được bảo hiểm", rules: [ruleInputMessage.required]},
  ngay_sinh: {component: "date-picker", label: "Ngày sinh", name: "ngay_sinh", placeholder: "Chọn ngày sinh", allowClear: false, rules: [ruleInputMessage.required]},
  gioi_tinh: {component: "select", label: "Giới tính", name: "gioi_tinh", placeholder: "Chọn giới tính", allowClear: false, rules: [ruleInputMessage.required]},
  so_cmt: {component: "input", label: "Số CMT/CCCD/HC", name: "so_cmt", placeholder: "Nhập số CMT/CCCD/HC", rules: [ruleInputMessage.required]},
  dthoai: {component: "input", label: "Số điện thoại", name: "dthoai", placeholder: "Nhập số điện thoại", rules: [ruleInputMessage.required]},
  email: {component: "input", label: "Email", name: "email", placeholder: "Nhập email"},
  dia_chi: {component: "input", label: "Địa chỉ", name: "dia_chi", placeholder: "Nhập địa chỉ", rules: [ruleInputMessage.required]},
  ngay_cap: {component: "date-picker", label: "Ngày cấp", name: "ngay_cap", placeholder: "Chọn ngày cấp", rules: [ruleInputMessage.required]},
  gcn: {component: "input", label: "Giấy chứng nhận", name: "gcn", placeholder: "Nhập giấy chứng nhận", rules: [ruleInputMessage.required]},
  vip: {component: "select", label: "Khách hàng VIP", name: "vip", placeholder: "Chọn loại khách hàng"},
  gio_hl: {component: "time-picker", label: "Giờ hiệu lực", name: "gio_hl", placeholder: "Chọn giờ hiệu lực"},
  ngay_hl: {component: "date-picker", label: "Ngày hiệu lực", name: "ngay_hl", placeholder: "Chọn ngày kết thúc", rules: [ruleInputMessage.required]},
  gio_kt: {component: "time-picker", label: "Giờ kết thúc", name: "gio_kt", placeholder: "Chọn giờ kết thúc"},
  ngay_kt: {component: "date-picker", label: "Ngày kết thúc", name: "ngay_kt", placeholder: "Chọn ngày kết thúc", rules: [ruleInputMessage.required]},
  ma_goi_bh: {component: "select", label: "Gói bảo hiểm", name: "ma_goi_bh", placeholder: "Chọn mã gói bảo hiểm", allowClear: false, rules: [ruleInputMessage.required]},
  cnhanh_ctac: {component: "input", label: "Chi nhánh công tác", name: "cnhanh_ctac", placeholder: "Nhập chi nhánh"},
  pban_ctac: {component: "input", label: "Phòng ban công tác", name: "pban_ctac", placeholder: "Nhập phòng ban"},
  cvu_ctac: {component: "input", label: "Chức vụ", name: "cvu_ctac", placeholder: "Nhập chức vụ"},
  cty_ctac: {component: "input", label: "Công ty công tác", name: "cty_ctac", placeholder: "Nhập công ty"},
  ma_nv_ctac: {component: "input", label: "Nhân viên công tác", name: "ma_nv_ctac", placeholder: "Nhập nhân viên"},
  email_ctac: {component: "input", label: "Email công tác", name: "email_ctac", placeholder: "Nhập email"},
};

export const listGioiTinhSelect: Array<{ma: string; ten: string}> = [
  {ma: "NAM", ten: "Nam"},
  {ma: "NU", ten: "Nữ"},
  {ma: "KHAC", ten: "Khác"},
];

export const listKhachHangVipSelect: Array<{ma: string; ten: string}> = [
  {ma: "SVIP", ten: "SVIP"},
  {ma: "VIP", ten: "VIP"},
  {ma: "", ten: "Không"},
];

/* MODAL TÌM CÁN BỘ QUẢN LÝ */
//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐỐI TÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableCanBoColumnDataType {
  key: string;
  sott?: number; //CỘT 1
  ma: string; //CỘT 1
  ten?: string; //CỘT 2
  ten_doi_tac?: string;
  dthoai?: string;
  email?: string;
  cmt?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableCanBoColumn: TableProps<TableCanBoColumnDataType>["columns"] = [
  {title: "STT", dataIndex: "sott", key: "sott", align: "center", width: "3%", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: "10%", ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: "20%", ...defaultTableColumnsProps},
  {title: "Đối tác", dataIndex: "ten_doi_tac", key: "ten_doi_tac", align: "center", width: "7%", ...defaultTableColumnsProps},
  {title: "Số điện thoại", dataIndex: "dthoai", key: "dthoai", align: "center", width: "10%", ...defaultTableColumnsProps},
  {title: "Email", dataIndex: "mail", key: "email", width: "30%", ...defaultTableColumnsProps},
  {title: "CMT/CCCD", dataIndex: "cmt", key: "cmt", align: "center", width: "8%", ...defaultTableColumnsProps},
  {title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: "10%", align: "center", ...defaultTableColumnsProps},
];

//keyof: return ra key của inteface TableKhachHangColumnDataType;
export type TableCanBoColumnDataIndex = keyof TableCanBoColumnDataType;

//radio trong table
export const radioItemTrangThaiCanBoTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngưng sử dụng", text: "Ngưng sử dụng"},
];

export interface ModalTimCanBoProps {
  maDoiTacSelected: string;
  listChiNhanhSelect: Array<CommonExecute.Execute.IChiNhanh>;
  maPhongBanSelected: string;
  listPhongBanSelect: Array<CommonExecute.Execute.IPhongBan>;
  onSelectCanBo: (ma_cb_ql: CommonExecute.Execute.IDoiTacNhanVien | null) => void;
}
// FORM TÌM KIẾM CÁN BỘ
export interface IFormTimKiemCannBoFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma_chi_nhanh_ql: IFormInput;
  phong_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  cmt: IFormInput;
  dthoai: IFormInput;
  email: IFormInput;
  nd_tim: IFormInput;
  trang_thai: IFormInput;
}

export const defaultFilterCanBoParams: ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams = {
  ma_doi_tac_ql: "",
  ma_chi_nhanh_ql: "",
  phong_ql: "",
  ma: "",
  ten: "",
  cmt: "",
  dthoai: "",
  email: "",
  nd_tim: "",
  trang: 1,
  so_dong: 10,
};

export const FormTimKiemCanBo: IFormTimKiemCannBoFieldsConfig = {
  ma_doi_tac_ql: {component: "select", label: "Đối tác", name: "ma_doi_tac_ql", rules: [ruleInputMessage.required], autoClearSearchValue: true, disabled: true},
  ma_chi_nhanh_ql: {component: "select", label: "Chi nhánh", name: "ma_chi_nhanh_ql", autoClearSearchValue: true, allowClear: true, placeholder: "Chọn chi nhánh"},
  phong_ql: {component: "select", name: "phong_ql", label: "Phòng ban", placeholder: "Chọn phòng ban"},
  ma: {component: "input", name: "ma", label: "Mã cán bộ", placeholder: "Nhập mã cán bộ"},
  ten: {component: "input", name: "ten", label: "Tên cán bộ quản lý ", placeholder: "Nhập tên cán bộ quản lý"},
  cmt: {component: "input", label: "CCCD/CMT", name: "cccd", placeholder: "Nhập CCCD/CMT"},
  dthoai: {component: "input", name: "dthoai", placeholder: "Nhập số điện thoại", rules: [ruleInputMessage.phone]},
  email: {component: "input", name: "email", placeholder: "Nhập email", rules: [ruleInputMessage.email]},
  nd_tim: {component: "input", name: "nd_tim", placeholder: "Nội dung tìm"},
  trang_thai: {component: "select", name: "trang_thai", placeholder: "Chọn trạng thái"},
};

export const radioItemTrangThaiCanBoSelect: Array<{ma: string; ten: string}> = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngưng sử dụng", ma: "K"},
];
//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalTimCanBoRef {
  open: (canBoSelected?: {ma: string; ten: string}) => void;
  close: () => void;
}

/* MODAL TÌM ĐẠI LÝ KHAI THÁC*/
//ĐỊNH NGHĨA CẤU HÌNH TABLE ĐẠI LÝ KHAI THÁC GỒM NHỮNG CỘT NÀO, THỨ TỰ CỘT SẼ HIỂN THỊ THEO THỨ TỰ KHAI BÁO Ở DƯỚI
export interface TableDaiLyKhaiThacColumnDataType {
  key: string;
  stt?: number;
  ma?: string;
  ma_doi_tac_ql?: string;
  doi_tac_ql_ten_tat?: string;
  ten?: string;
  doi_tac_ql?: string;
  loai?: string;
  ten_loai?: string;
  ten_dd?: string;
  so_dien_thoai?: string;
  email?: string;
  mst?: string;
  cmt?: string;
  ngay_tao?: string;
  nguoi_tao?: string;
  ngay_cap_nhat?: string;
  nguoi_cap_nhat?: string;
  trang_thai_ten?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableDaiLyKhaiThacColumn: TableProps<TableDaiLyKhaiThacColumnDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: 80, align: "center", ...defaultTableColumnsProps},
  {title: "Mã", dataIndex: "ma", key: "ma", width: 200, align: "center", ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 300, align: "left", ...defaultTableColumnsProps},
  {title: "Đối tác", dataIndex: "doi_tac_ql_ten_tat", key: "doi_tac_ql_ten_tat", width: 300, align: "center", ...defaultTableColumnsProps},
  {title: "Loại đại lý", dataIndex: "ten_loai", key: "ten_loai", width: 150, align: "center", ...defaultTableColumnsProps},
  {title: "Tên đại diện", dataIndex: "nguoi_dd", key: "nguoi_dd", width: 300, align: "center", ...defaultTableColumnsProps},
  {title: "Số điện thoại", dataIndex: "dthoai_dd", key: "dthoai_dd", width: 200, align: "center", ...defaultTableColumnsProps},
  {title: "Email", dataIndex: "email_dd", key: "email_dd", width: 300, align: "center", ...defaultTableColumnsProps},
  {title: "Mã số thuế", dataIndex: "mst_dd", key: "mst_dd", width: 200, align: "center", ...defaultTableColumnsProps},
  {title: "CMT/CCCD", dataIndex: "cmt_dd", key: "cmt_dd", width: 200, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao, align: "center", ...defaultTableColumnsProps},
  {title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center", ...defaultTableColumnsProps},
  {title: "Trạng Thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 200, align: "center", ...defaultTableColumnsProps},
];

//keyof: return ra key của inteface TableKhachHangColumnDataType;
export type TableDaiLyKhaiThacColumnDataIndex = keyof TableDaiLyKhaiThacColumnDataType;

//radio trong table
export const radioItemTrangThaiDaiLyKhaiThacTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];

export interface ModalTimDaiLyKhaiThacProps {
  maDoiTacSelected: string;
  onSelectDaiLyKhaiThac: (ma_cb_ql: CommonExecute.Execute.IDanhMucDaiLy | null) => void;
}
// FORM TÌM KIẾM ĐẠI LÝ KHAI THÁC
export interface IFormTimKiemDaiLyKhaiThacFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  loai: IFormInput;
  trang_thai: IFormInput;
}

export const defaultFilterDaiLyKhaiThacParams: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams = {ma_doi_tac_ql: "", ma: "", ten: "", loai: "", trang_thai: "", trang: 1, so_dong: 10};

export const FormTimKiemDaiLyKhaiThac: IFormTimKiemDaiLyKhaiThacFieldsConfig = {
  ma_doi_tac_ql: {component: "select", label: "Đối tác", name: "ma_doi_tac_ql", rules: [ruleInputMessage.required], autoClearSearchValue: true, disabled: true, className: "!mb-0"},
  ma: {component: "input", name: "ma", label: "Mã đại lý", placeholder: "Nhập mã đại lý", className: "!mb-0"},
  ten: {component: "input", name: "ten", label: "Tên đại lý", placeholder: "Nhập tên đại lý", className: "!mb-0"},
  loai: {component: "select", name: "loai", label: "Loại đại lý", placeholder: "Chọn loại đại lý", className: "!mb-0"},
  trang_thai: {component: "select", name: "trang_thai", label: "Trạng thái", placeholder: "Chọn trạng thái", className: "!mb-0"},
};

export const listTrangThaiSelect: Array<{ma: string; ten: string}> = [
  {ten: "Đang sử dụng", ma: "D"},
  {ten: "Ngưng sử dụng", ma: "K"},
];
export const listLoaiDaiLySelect: Array<{ma: string; ten: string}> = [
  {ten: "Đại lý tổ chức", ma: "T"},
  {ten: "Đại lý cá nhân", ma: "C"},
];
//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalTimDaiLyKhaiThacRef {
  open: (daiLySelected?: {ma: string; ten: string}) => void;
  close: () => void;
}

/* THÊM HỢP ĐỒNG BƯỚC 2 : NHẬP ĐỐI TƯỢNG BẢO HIỂM */
export const defaultParamsTimKiemPhanTrangNguoiDuocBaoHiem: ReactQuery.ITimKiemPhanTrangNguoiDuocBaoHiemHopDongConNguoi = {
  so_id: 0,
  so_hd: "",
  ten: "",
  ngay_sinh: "",
  so_cmt: "",
  dthoai: "",
  nd_tim: "",
  trang: 1,
  so_dong: 10,
};

export interface TableNguoiDuocBaoHiemColumnDataType {
  key: string;
  ten?: string;
  ngay_hl?: string;
  so_id: string;
  so_id_dt: string | number;
  tong_phi?: number;
}

export const tableNguoiDuocBaoHiemColumn: TableProps<TableNguoiDuocBaoHiemColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "Họ tên / CMT",
    dataIndex: "ten",
    key: "ten",
    width: "50%",
    align: "left",
    ellipsis: true,
    render: (text: any, record: any, index: number) => (text !== undefined ? text : "\u00A0"),
  },
  {
    ...defaultTableColumnsProps,
    title: "Phí BH",
    dataIndex: "tong_phi",
    key: "tong_phi",
    width: "25%",
    align: "right",
    ellipsis: true,
    render: (text: any) => <NumericFormat value={text} displayType="text" thousandSeparator="," decimalSeparator="." decimalScale={2} style={{textAlign: "right"}} />,
  },
];

export type TableNguoiDuocBaoHiemColumnDataIndex = keyof TableNguoiDuocBaoHiemColumnDataType;

export interface IFormTimKiemPhanTrangNguoiDuocBaoHiemFieldsConfig {
  nd_tim?: IFormInput;
  dong_tai?: IFormInput;
}
//option select đối tượng áp dụng
export const listDoiTuongApDungDongBH = [
  {ten: "Tất cả", ma: ""},
  {ten: "Đối tượng áp dụng", ma: "DONG_BH"},
];

// KHƠI TẠO FORM TÌM KIẾM ĐỐI TƯỢNG
export const FormTimKiemNguoiDuocBaoHiem: IFormTimKiemPhanTrangNguoiDuocBaoHiemFieldsConfig = {
  nd_tim: {component: "input", name: "nd_tim", placeholder: "Họ tên / CMT / Điện thoại"},
  dong_tai: {component: "select", name: "dong_tai", placeholder: "Chọn đối tượng áp dụng", options: listDoiTuongApDungDongBH},
};

/*TAB THÔNG TIN NGƯỜI ĐƯỢC BẢO HIỂM */
export interface ThemHopDongStep2_TabThongTinNguoiDuocBaoHiemProps {
  formThongTinNguoiBaoHiem: FormInstance<any>;
  setIsChangeData: React.Dispatch<React.SetStateAction<boolean>>;
}
export interface ThemHopDongStep2_TabThongTinNguoiDuocBaoHiemRef {}

/*TAB QUYỀN LỢI CHÍNH VÀ QUYỀN LỢI BỔ SUNG */
export interface ThemHopDongStep2_TabQuyenLoiProps {
  setIsChangeData: React.Dispatch<React.SetStateAction<boolean>>;
  tabActive: string;
}
export interface ThemHopDongStep2_TabQuyenLoiRef {
  getListQuyenLoiNguoiDuocBaoHiem: () => CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
  getCheckboxApDungGoiBaohiem: () => boolean;
}

export interface TableQuyenLoiColumnDataType {
  key: string;
  stt: number;
  ten_qloi?: string; //TÊN QUYỀN LỢI
  gh_lan_ngay?: number; //GIỚI HẠN LẦN NGÀY
  gh_tien_lan_ngay?: number; //GIỚI HẠN TIỀN LẦN NGÀY
  gh_tien_nam?: number; //GIỜ HẠN TIỀN NĂM
  tgian_cho?: number; //THỜI GIAN CHỜ
  // kieu_ad_ten?: string; //KIỂU ÁP DỤNG
  kieu_ad?: string; //MÃ KIỂU ÁP DỤNG
  gh_lan_ngay_dtri?: number; //GIỚI HẠN LẦN NGÀY ĐIỀU TRỊ
  gh_tien_lan_ngay_dtri?: number; //GIỚI HẠN TIỀN LẦN NGÀY ĐIỀU TRỊ
  nt_tien_bh?: string; //NGUYÊN TỆ TIỀN BẢO HIỂM
  ma_qloi_tru_lui?: string; //MÃ QUYỀN LỢI TRỪ LÙI
  ma_qloi: string;
  tl_dct?: number; //Tỉ lệ đồng chi trả
  phi_bh?: number; //PHÍ BẢO HIỂM
  ghi_chu?: string; //GHI CHÚ
  cap?: number;
}

export const tableQuyenLoiColumn: TableProps<TableQuyenLoiColumnDataType>["columns"] = [
  {title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, ...defaultTableColumnsProps},
  {title: "Tên quyền lợi", dataIndex: "ten_qloi", key: "ten_qloi", width: 350, ...defaultTableColumnsProps, align: "left"},
  {title: "G.hạn số\nlần/ngày", dataIndex: "gh_lan_ngay", key: "gh_lan_ngay", width: 100, ...defaultTableColumnsProps, ellipsis: false},
  {title: "G.hạn tiền \nlần/ngày", dataIndex: "gh_tien_lan_ngay", key: "gh_tien_lan_ngay", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "G.hạn \ntiền/năm", dataIndex: "gh_tien_nam", key: "gh_tien_nam", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "T.gian \nchờ", dataIndex: "tgian_cho", key: "tgian_cho", width: 100, ...defaultTableColumnsProps, ellipsis: false},
  {title: "Kiểu áp dụng", dataIndex: "kieu_ad", key: "kieu_ad", width: 130, ...defaultTableColumnsProps},
  {title: "G.hạn lần/ngày \n1 lần đ.trị", dataIndex: "gh_lan_ngay_dtri", key: "gh_lan_ngay_dtri", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "G.hạn tiền lần/ngày \n1 lần đ.trị", dataIndex: "gh_tien_lan_ngay_dtri", key: "gh_tien_lan_ngay_dtri", width: 130, ...defaultTableColumnsProps, ellipsis: false},
  {title: "N.tệ STBH", dataIndex: "nt_tien_bh", key: "nt_tien_bh", width: 110, ...defaultTableColumnsProps},
  {title: "Q.lợi \ntrừ lùi", dataIndex: "ma_qloi_tru_lui", key: "ma_qloi_tru_lui", width: 80, ...defaultTableColumnsProps, ellipsis: false},
  {title: "Tỉ lệ \n đồng chi trả", dataIndex: "tl_dct", key: "tl_dct", width: 80, ...defaultTableColumnsProps, ellipsis: false},
  {title: "Phí BH", dataIndex: "phi_bh", key: "phi_bh", width: 120, ...defaultTableColumnsProps},
  {title: "Ghi chú", dataIndex: "ghi_chu", key: "ghi_chu", width: 100, ...defaultTableColumnsProps},
];

//keyof: return ra key của inteface TableGoiBaoHiemColumnDataIndex;
export type TableQuyenLoiColumnDataIndex = keyof TableQuyenLoiColumnDataType;

export const listKieuApDung: Array<{ma: string; ten: string}> = [
  {ma: "TIEN", ten: "Tiền"},
  {ma: "LAN_NGAY", ten: "Lần ngày"},
  {ma: "THANG_LUONG", ten: "Tháng lương"},
  {ma: "NGAY_LUONG", ten: "Ngày lương"},
];

export interface ThemHopDongStep2_TabQuyenLoiBoSungProps {
  setIsChangeData: React.Dispatch<React.SetStateAction<boolean>>;
  tabActive: string;
}
export interface ThemHopDongStep2_TabQuyenLoiBoSungRef {
  getListQuyenLoiNguoiDuocBaoHiem: () => CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
  getCheckboxApDungGoiBaohiem: () => boolean;
}

/* MODAL QUYỀN LỢI PHỤ THUỘC */
export interface IModalThemQuyenLoiRef {}
export interface ModalThemQuyenLoiProps {
  quyenLoiDangXem?: CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi;
  listQuyenLoiRoot: CommonExecute.Execute.IDanhSachBoMaQuyenLoi[];
  listQuyenLoi: CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
  checkStrictly: boolean;
  setIsOpenQuyenLoiTruLui: Dispatch<SetStateAction<boolean>>;
  onPressLuuQuyenLoiTruLui: (listMaQuyenLoiSelect: string[], maQuyenLoiDangXem?: string) => void;
  onPressThemQuyenLoi?: (quyenLoiDuocThem: string[]) => void;
}

// TAB NGƯỜI PHỤ THUỘC
export interface ThemHopDongStep2_TabNguoiPhuThuocProps {
  tabActive: string;
  chiTietHopDong: CommonExecute.Execute.IHopDongConNguoi | null;
}
export interface ThemHopDongStep2_TabNguoiPhuThuocRef {
  openModalChiTietNguoiPhuThuoc: () => void;
  // getListQuyenLoiNguoiDuocBaoHiem: () => CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
  // getCheckboxApDungGoiBaohiem: () => boolean;
}
export interface ChiTietNguoiPhuThuocProps {
  chiTietHopDong: CommonExecute.Execute.IHopDongConNguoi | null;
  listNguoiPhuThuoc: CommonExecute.Execute.IChiTietNguoiPhuThuoc[];
  initListNguoiPhuThuoc: () => void;
}

//định nghĩa các hàm, biến mà modal sẽ expose để component bên ngoài sử dụng thông qua ref
export interface IModalChiTietNguoiPhuThuocRef {
  open: (data?: any) => void;
  close: () => void;
}
export interface IFormTaoMoiNguoiPhuThuocFieldsConfig {
  ten: IFormInput;
  ngay_sinh: IFormInput;
  gioi_tinh: IFormInput;
  so_cmt: IFormInput;
  moi_qhe: IFormInput;
  dthoai: IFormInput;
  email: IFormInput;
}

export const FormTaoMoiNguoiPhuThuoc: IFormTaoMoiNguoiPhuThuocFieldsConfig = {
  ten: {component: "input", label: "Tên", name: "ten", placeholder: "Nhập tên người phụ thuôc", rules: [ruleInputMessage.required]},
  ngay_sinh: {component: "date-picker", label: "Ngày sinh", name: "ngay_sinh", placeholder: "Chọn ngày sinh", rules: [ruleInputMessage.required]},
  gioi_tinh: {component: "select", label: "Giới tính", name: "gioi_tinh", placeholder: "Chọn giới tính", rules: [ruleInputMessage.required]},
  so_cmt: {component: "input", label: "Số CMT/CCCD", name: "so_cmt", placeholder: "Nhập CMT / CCCD", rules: [ruleInputMessage.required]},
  moi_qhe: {component: "select", label: "Chọn mối quan hệ", name: "moi_qhe", placeholder: "Chọn mối quan hệ", rules: [ruleInputMessage.required]},
  dthoai: {component: "input", label: "Điện thoại", name: "dthoai", placeholder: "Nhập điện thoại", rules: [ruleInputMessage.required, ruleInputMessage.phone]},
  email: {component: "input", label: "Email", name: "email", placeholder: "Nhâp email", rules: [ruleInputMessage.email]},
};

export const listMoiQuanHeSelect: Array<{ma: string; ten: string}> = [
  {ma: "ONG_BA", ten: "Ông/bà"},
  {ma: "BO_ME", ten: "Bố/mẹ"},
  {ma: "CON", ten: "Con ruột"},
  {ma: "ACE", ten: "Anh/chị/em"},
  {ma: "KHAC", ten: "Khác"},
];

export interface TableNguoiPhuThuocColumnDataType {
  key: number;
  so_id?: number;
  so_id_dt?: number;
  bt?: number;
  sott?: number;
  ten?: string;
  ngay_sinh?: string;
  gioi_tinh_ten?: string;
  so_cmt?: string;
  moi_qhe_ten?: string;
  dthoai?: string;
  email?: string;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableNguoiPhuThuocColumn: TableProps<TableNguoiPhuThuocColumnDataType>["columns"] = [
  {title: "STT", dataIndex: "sott", key: "sott", align: "center", width: colWidthByKey.sott, ...defaultTableColumnsProps},
  {title: "Tên", dataIndex: "ten", key: "ten", width: 250, ...defaultTableColumnsProps},
  {title: "Ngày sinh", dataIndex: "ngay_sinh", key: "ngay_sinh", width: 100, ...defaultTableColumnsProps},
  {title: "Giới tính", dataIndex: "gioi_tinh_ten", key: "gioi_tinh", width: 100, ...defaultTableColumnsProps},
  {title: "Số CMT", dataIndex: "so_cmt", key: "so_cmt", width: 130, ...defaultTableColumnsProps},
  {title: "Mối quan hệ", dataIndex: "moi_qhe_ten", key: "moi_qhe_ten", width: 130, ...defaultTableColumnsProps},
  {title: "Điện thoại", dataIndex: "dthoai", key: "dthoai", align: "center", width: 100, ...defaultTableColumnsProps},
  {title: "Email", dataIndex: "email", key: "email", align: "center", width: 200, ...defaultTableColumnsProps},
];

export type TableNguoiPhuThuocColumnDataIndex = keyof TableNguoiPhuThuocColumnDataType;

/* STEP THÔNG TIN THANH TOÁN */

/* THÊM HỢP ĐỒNG BƯỚC 3 */
export interface ThemHopDongStep3Props {}

export interface IThemHopDongStep3Ref {}

export interface FileCategory {
  id: string;
  name: string;
  files: {type: string; url: string; name: string; id?: number}[];
}

export const NGHIEP_VU_NG = "NG";

export const UNCLASSIFIED_ID = "UNCLASSIFIED";
export const UNCLASSIFIED_NAME = "Ảnh chưa phân loại";

export interface FileSelectedViewComponentProps {
  selectedFile: any;
  getCurrentFileIndex: () => number;
  getTotalFiles: () => number;
  navigateToPrevious: () => void;
  navigateToNext: () => void;
}

export interface ListFileViewComponentProps {
  selectedFile: any;
  categories: FileCategory[];
  selectedFiles: string[];
  setSelectedFiles: Dispatch<SetStateAction<string[]>>;
  selectedCategory: string | undefined;
  setSelectedCategory: Dispatch<SetStateAction<string | undefined>>;
  setSelectedFile: Dispatch<any>;
  getCategoryByFile: (file: any) => FileCategory | undefined;
  fileInputRef: RefObject<HTMLInputElement>;
  phanLoaiPopoverVisible: boolean;
  setPhanLoaiPopoverVisible: Dispatch<SetStateAction<boolean>>;
  handleUploadClick: () => void;
  handlePhanLoaiFiles: (ma_hang_muc: string) => Promise<void>;
  handleDeleteFiles: () => Promise<void>;
  handleFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

/* THÊM HỢP ĐỒNG BƯỚC 4 */
export interface ThemHopDongStep4Props {}

export interface IThemHopDongStep4Ref {}

//TAB THÔNG TIN KỲ THANH TOÁN
export interface TableThongTinKyThanhToanDataType {
  bt: number;
  ky_tt: number;
  ky_tt_date: string;
  loai: string;
  loai_ten: string | null;
  ma_doi_tac: string;
  ma_doi_tac_ql: string;
  ngay_cap_nhat: string | null;
  ngay_tao: string | null;
  ngay_tt: string | null;
  ngay_tt_date: string;
  nguoi_cap_nhat: string | null;
  nguoi_tao: string | null;
  so_hd: string | null;
  so_id: number | null;
  so_id_d: number;
  so_id_g: number | null;
  so_id_ky_ttoan: number;
  so_tien: number;
  so_tien_da_tt: number | null;
  trang_thai: string | null;
  key: string;
  sott: number;
  rowSpanKyTT?: number;
  rowSpanSoTien?: number;
  sttKyTT?: number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableThongTinThanhToanColumn: TableProps<TableThongTinKyThanhToanDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "sttKyTT",
    key: "sttKyTT",
    width: colWidthByKey.sott,
    align: "center",
    onCell: (record: any) => ({rowSpan: record.rowSpanKyTT ?? 1, className: "!py-1 text-[11px]"}),
    render: (text: any, record: any) => (record.sttKyTT ? record.sttKyTT : null),
  },
  {
    ...defaultTableColumnsProps,
    title: "Kỳ thanh toán",
    dataIndex: "ky_tt_date",
    key: "ky_tt_date",
    width: 100,
    align: "center",
    onCell: (record: any) => ({rowSpan: record.rowSpanKyTT ?? 1, className: "!py-1 text-[11px] font-semibold"}),
    render: (text: any, record: any, index: number) => {
      return text !== undefined ? text : "\u00A0";
      // return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px] "}, "");
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Số tiền",
    dataIndex: "so_tien",
    key: "so_tien",
    width: 130,
    align: "right",
    ellipsis: true,
    render: (text: any) => {
      if (typeof text === "number") return formatCurrencyUS(text);
      return text;
    },
    onCell: (record: any) => ({rowSpan: record.rowSpanSoTien ?? 1, className: "!py-1 text-[11px] !font-semibold"}),
  },
  {
    ...defaultTableColumnsProps,
    title: "Loại",
    dataIndex: "loai_ten",
    key: "loai_ten",
    width: 100,
    align: "center",
    render: (text: any, record: any) => {
      if (text === "Tăng phí") return <span style={{color: "#52c41a"}}>{text}</span>;
      if (text === "Hoàn phí") return <span style={{color: "#ff4d4f"}}>{text}</span>;
      return record.loai_ten;
    },
  },
  {...defaultTableColumnsProps, title: "Ngày thanh toán", dataIndex: "ngay_tt_date", key: "ngay_tt_date", width: 120, align: "center"},
  {
    ...defaultTableColumnsProps,
    title: "Tiền đã thanh toán",
    dataIndex: "so_tien_da_tt",
    key: "so_tien_da_tt",
    width: 140,
    align: "right",
    ellipsis: true,
    render: (text: any) => {
      if (typeof text === "number") {
        if (text < 0) return <span style={{color: "#ff4d4f"}}>{formatCurrencyUS(text)}</span>;
        return formatCurrencyUS(text);
      }
      return text;
    },
  },
  {...defaultTableColumnsProps, title: "Số chứng từ", dataIndex: "so_ct", key: "so_ct", width: 140, align: "center"},
  {...defaultTableColumnsProps, title: "Số HĐ/SĐBS", dataIndex: "so_hd", key: "so_hd", align: "center"},
  {...defaultTableColumnsProps, title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_cap_nhat, align: "center"},
  {...defaultTableColumnsProps, title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_cap_nhat, align: "center"},
  {...defaultTableColumnsProps, title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat, align: "center"},
  {...defaultTableColumnsProps, title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat, align: "center"},
];

export interface IThongTinThanhToanStep4Ref {}
export interface ThongTinThanhToanStep4Props {}

// MODAL KỲ THANH TOÁN
export interface IFormNhapKyThanhToan {
  ky_tt: IFormInput;
  so_tien: IFormInput;
  so_hd_d: IFormInput;
}
export const formNhapKyThanhToanInputConfigs: IFormNhapKyThanhToan = {
  ky_tt: {
    name: "ky_tt",
    component: "date-picker",
    label: "Kỳ thanh toán",
    placeholder: "Chọn kỳ thanh toán",
    rules: [ruleInputMessage.required],
  },
  so_tien: {
    name: "so_tien",
    component: "input-price",
    label: "Số tiền",
    placeholder: "0",
    rules: [ruleInputMessage.required],
  },
  so_hd_d: {
    name: "so_hd_d",
    component: "input",
    label: "Số HĐ đầu",
    placeholder: "Nhập số HĐ đầu",
    rules: [ruleInputMessage.required],
    disabled: true,
  },
};

//TAB NGÀY THANH TOÁN
export interface TableNgayThanhToanDataType {
  key: string;
  stt: number;
  ngay_tt: string;
  so_tien_da_tt: number;
  bt: number;
  ten_loai: string;
  so_ct: string;
}

export const tableNgayThanhToanColumns: TableProps<TableNgayThanhToanDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, className: "no-hover-table"},
  {...defaultTableColumnsProps, title: "Ngày thanh toán", dataIndex: "ngay_tt", key: "ngay_tt", className: "no-hover-table"},
  {...defaultTableColumnsProps, title: "Số chứng từ", dataIndex: "so_ct", key: "so_ct", width: 150, className: "no-hover-table"},
  {
    ...defaultTableColumnsProps,
    title: "Số tiền đã thanh toán",
    dataIndex: "so_tien_da_tt",
    key: "so_tien_da_tt",
    width: 150,
    align: "right",
    ellipsis: true,
    render: (text: any) => {
      if (typeof text === "number") return formatCurrencyUS(text);
      return text;
    },
  },
  {...defaultTableColumnsProps, title: "Tên loại", dataIndex: "ten_loai", key: "ten_loai", width: 80},
  {
    ...defaultTableColumnsProps,
    dataIndex: "action",
    key: "action",
    width: 80,
  },
];

export interface IModalKyThanhToanRef {
  open: (dataKyThanhToan?: CommonExecute.Execute.IKyThanhToan, ky_ttoan_ct?: CommonExecute.Execute.IKyThanhToan) => void;
  close: () => void;
}

export interface IThongTinDongBaoHiemStep4Ref {}
export interface ThongTinDongBaoHiemStep4Props {}

//INTERFACE TT IN ĐỒNG BẢO HIỂM TABLE
export interface TableCauHinhDongBaoHiemDataType {
  kieu_dong?: string;
  loai_dong?: string;
  loai_dong_ten?: string;
  ma_doi_tac?: string;
  ma_doi_tac_ql?: string;
  ma_dvi_dong?: string;
  ten_dvi_dong?: string;
  nv?: string;
  sl_dt_bh?: number;
  sl_goi_bh?: number;
  sl_qloi_bh?: number;
  tl_dong?: number;
  sott?: number;
  key: string;
  so_tham_chieu?: string;
  tien_nhuong_dong?: number;
  tien_con?: number;
}

export const tableCauHinhDongBaoHiemColumn: TableProps<TableCauHinhDongBaoHiemDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "sott", key: "sott", width: colWidthByKey.sott},
  {
    ...defaultTableColumnsProps,
    title: "Loại đồng",
    dataIndex: "loai_dong_ten",
    key: "loai_dong_ten",
    width: 90,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      // return text !== undefined ? text : React.createElement(Tag, {color: "transparent", className: "!text-white text-[11px]"}, "");
      return text !== undefined ? text : "\u00A0";
    },
  },
  {...defaultTableColumnsProps, title: "Kiểu đồng", dataIndex: "kieu_dong", key: "kieu_dong", width: 90},
  {...defaultTableColumnsProps, title: "Tên đơn vị đồng", dataIndex: "ten_dvi_dong", key: "ten_dvi_dong"},
  {...defaultTableColumnsProps, title: "TL đồng (%)", dataIndex: "tl_dong", key: "tl_dong", width: 100},
  {...defaultTableColumnsProps, title: "Số tham chiếu/Hợp đồng/SĐBS", dataIndex: "so_tham_chieu", key: "so_tham_chieu", width: 200},
  {
    ...defaultTableColumnsProps,
    title: "Tiền nhượng đồng",
    dataIndex: "tien_nhuong_dong",
    key: "tien_nhuong_dong",
    width: 150,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") return formatCurrencyUS(text);
      return text;
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Tiền giữ lại",
    dataIndex: "tien_con",
    key: "tien_con",
    width: 150,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") return formatCurrencyUS(text);
      return text;
    },
  },
  {...defaultTableColumnsProps, title: "Đối tượng áp dụng", dataIndex: "sl_dt_bh", key: "sl_dt_bh", align: "center", width: 200},
  {...defaultTableColumnsProps, dataIndex: "action", key: "action", width: 60},
];

//option select kiểu đồng
export const listKieuDong = [
  {ten: "LEADER", ma: "LEADER"},
  {ten: "FOLLOW", ma: "FOLLOW"},
];

//option select loại đồng
export const listLoaiDong = [
  {ten: "Đồng trong", ma: "T"},
  {ten: "Đồng ngoài", ma: "N"},
];
export interface IFormNhapCauHinhDong {
  ma_dvi_dong: IFormInput;
  loai_dong: IFormInput;
  kieu_dong: IFormInput;
  so_id: IFormInput;
  tl_dong: IFormInput;
  so_tham_chieu: IFormInput;
}
export const formCauHinhDongInputConfigs: IFormNhapCauHinhDong = {
  so_id: {name: "so_id", className: "hidden"},
  ma_dvi_dong: {name: "ma_dvi_dong", component: "select", label: "Mã đơn vị đồng", placeholder: "Mã đơn vị", rules: [ruleInputMessage.required]},
  loai_dong: {name: "loai_dong", component: "select", label: "Loại đồng", placeholder: "Chọn loại đồng", rules: [ruleInputMessage.required]},
  kieu_dong: {name: "kieu_dong", component: "select", label: "Kiểu đồng NBH", placeholder: "Nhập số HĐ đầu", rules: [ruleInputMessage.required]},
  tl_dong: {name: "tl_dong", component: "input", label: "Tỷ lệ đồng", placeholder: "0", className: "!text-right", rules: [ruleInputMessage.required]},
  so_tham_chieu: {name: "so_tham_chieu", component: "input", label: "Số tham chiếu/Hợp đồng/SĐBS", placeholder: "Số tham chiếu/Hợp đồng/SĐBS", rules: [ruleInputMessage.required]},
};

export interface IModalCauHinhDongBHRef {
  open: (data?: CommonExecute.Execute.IDongBaoHiem) => void;
  close: () => void;
}

export interface TableDoiTuongApDungColumnDataType {
  key: string;
  ap_dung: string;
  ten?: string;
  so_id: string;
  so_id_dt: string | number;
  tl_dong?: string | number;
  tl_tai?: string | number;
}

export const tableDoiTuongApDungDongBHColumn: TableProps<TableDoiTuongApDungColumnDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "sott", key: "sott", width: "10%"},
  {...defaultTableColumnsProps, title: "Áp dụng", dataIndex: "ap_dung", key: "ap_dung", width: "15%"},
  {
    ...defaultTableColumnsProps,
    title: "Tên / CMT / Điện thoại",
    dataIndex: "ten",
    key: "ten",
    width: "50%",
    align: "left",
    ellipsis: true,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : "\u00A0";
    },
  },
  {...defaultTableColumnsProps, title: "Tỷ lệ", dataIndex: "tl_dong", key: "tl_dong", width: "20%"},
];

export interface IModalDoiTuongApDungRef {
  open: (donViDong?: CommonExecute.Execute.IDongBaoHiem, listDoiTuongDaDuocApDungDongBH?: []) => void;
  close: () => void;
}

export interface IThongTinTaiBaoHiemStep4Ref {}
export interface ThongTinTaiBaoHiemStep4Props {}

export interface TableCauHinhTaiBHDataType {
  key?: string | number;
  kieu_tai?: string;
  kieu_tai_ten?: string;
  ma_doi_tac?: string;
  ma_doi_tac_ql?: string;
  ma_dvi_tai?: string;
  ten_dvi_tai?: string;
  nv?: string;
  sl_dt_bh?: number;
  sl_goi_bh?: number;
  sl_qloi_bh?: number;
  sott?: number;
  tl_tai?: number;
  so_tham_chieu?: string;
  tien_nhuong_tai?: number;
  tien_con?: number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableCauHinhTaiBHColumn: TableProps<TableCauHinhTaiBHDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "sott", key: "sott", width: colWidthByKey.sott},

  {
    ...defaultTableColumnsProps,
    title: "Kiểu tái",
    dataIndex: "kieu_tai_ten",
    key: "kieu_tai_ten",
    width: 100,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : "\u00A0";
    },
  },
  {...defaultTableColumnsProps, title: "Tên đơn vị tái", dataIndex: "ten_dvi_tai", key: "ten_dvi_tai"},
  {...defaultTableColumnsProps, title: "TL tái (%)", dataIndex: "tl_tai", key: "tl_tai", width: 100},
  {...defaultTableColumnsProps, title: "Số tham chiếu/Hợp đồng/SĐBS", dataIndex: "so_tham_chieu", key: "so_tham_chieu", width: 250},
  {
    ...defaultTableColumnsProps,
    title: "Tiền nhượng tái",
    dataIndex: "tien_nhuong_tai",
    key: "tien_nhuong_tai",
    width: 150,
    align: "right",
    render: (text: any) => {
      if (typeof text === "number") return formatCurrencyUS(text);
      return text;
    },
  },
  // {
  //   ...defaultTableColumnsProps,
  //   title: "Tiền giữ lại",
  //   dataIndex: "tien_con",
  //   key: "tien_con",
  //   width: 150,
  //   align: "right",
  //   render: (text: any) => {
  //     if (typeof text === "number") {
  //       return formatCurrencyUS(text);
  //     }
  //     return text;
  //   },
  // },
  {...defaultTableColumnsProps, title: "Đối tượng áp dụng", dataIndex: "sl_dt_bh", key: "sl_dt_bh", align: "center", width: 200},
  {...defaultTableColumnsProps, dataIndex: "action", key: "action", width: colWidthByKey.sott},
];

export interface IFormNhapCauHinhTaiBH {
  ma_dvi_tai: IFormInput;
  kieu_tai: IFormInput;
  so_id: IFormInput;
  tl_tai: IFormInput;
  so_tham_chieu: IFormInput;
}
//option select kiểu đồng
export const listKieuTai = [
  {ten: "Tái cố định", ma: "C"},
  {ten: "Tái tạm thời", ma: "T"},
];

export const formCauHinhTaiBHInputConfigs: IFormNhapCauHinhTaiBH = {
  so_id: {name: "so_id", className: "hidden"},
  ma_dvi_tai: {name: "ma_dvi_tai", component: "select", label: "Mã đơn vị tái", placeholder: "Mã đơn vị", rules: [ruleInputMessage.required]},
  kieu_tai: {name: "kieu_tai", component: "select", label: "Kiểu tái NBH", placeholder: "Chọn kiểu tái", rules: [ruleInputMessage.required]},
  tl_tai: {name: "tl_tai", component: "input", label: "Tỷ lệ tái", placeholder: "0", className: "!text-right", rules: [ruleInputMessage.required]},
  so_tham_chieu: {name: "so_tham_chieu", component: "input", label: "Số tham chiếu/Hợp đồng/SĐBS", placeholder: "Số tham chiếu/Hợp đồng/SĐBS", rules: [ruleInputMessage.required]},
};

export interface IModalDoiTuongApDungTaiBHRef {
  open: (donViTaiBH?: CommonExecute.Execute.ITaiBaoHiem, listDonViDaDuocApDungTaiBH?: []) => void;
  close: () => void;
}

export const tableDoiTuongApDungTaiBHColumn: TableProps<TableDoiTuongApDungColumnDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "sott", key: "sott", width: "10%"},
  {...defaultTableColumnsProps, title: "Áp dụng", dataIndex: "ap_dung", key: "ap_dung", width: "15%"},
  {
    ...defaultTableColumnsProps,
    title: "Tên / CMT / Điện thoại",
    dataIndex: "ten",
    key: "bien_xe",
    width: "50%",
    align: "left",
    ellipsis: true,
    render: (text: any, record: any, index: number) => {
      // if (record.key && record.key.toString().includes("empty")) return "";
      return text !== undefined ? text : "\u00A0";
    },
  },
  {...defaultTableColumnsProps, title: "Tỷ lệ", dataIndex: "tl_tai", key: "tl_tai", width: "20%"},
];

/* TAB ĐÁNH GIÁ SỨC KHOẺ */
export interface ThemHopDongStep2_TabDanhGiaSucKhoeProps {
  setIsChangeData: React.Dispatch<React.SetStateAction<boolean>>;
  tabActive: string;
}

export interface ThemHopDongStep2_TabDanhGiaSucKhoeRef {
  getDanhSachCauHoiDanhGiaSucKhoe: () => IDanhGiaSucKhoeCauHoi[];
  getDanhSachCauTraLoiDanhGiaSucKhoe: () => IDanhGiaSucKhoeCauTraLoi[];
  getDataForSave: () => {dgsk: Array<{ma?: string; dap_an?: string}>};
  validateData: () => {isValid: boolean; errors: string[]};
  refreshData: () => void;
}

// Interface cho câu hỏi đánh giá sức khoẻ từ API response
export interface IDanhGiaSucKhoeCauHoi {
  ma?: string | string[]; // Có thể là string đơn lẻ hoặc mảng string
  ten?: string;
  kieu_chon?: string; // RADIO, CHECKBOX, INPUT, etc.
  bat_buoc?: string; // "C" = bắt buộc, "K" = không bắt buộc
  do_rong?: string; // COL-24, COL-12, etc.
  stt?: number;
  trang_thai?: string; // "D" = đang sử dụng, "K" = ngưng sử dụng
  ma_doi_tac?: string;
  so_id?: number;
  so_id_dt?: number;
  dap_an?: string | string[] | null; // Có thể là string đơn lẻ hoặc mảng string
}

// Interface cho câu trả lời đánh giá sức khoẻ
export interface IDanhGiaSucKhoeCauTraLoi {
  ma?: string | string[]; // Mã câu hỏi - có thể là string đơn lẻ hoặc mảng string
  cau_tra_loi?: string;
  ten_gia_tri?: string; // Tên hiển thị cho người dùng
  ghi_chu?: string;
}

// Interface cho dữ liệu hiển thị trong table
export interface TableDanhGiaSucKhoeColumnDataType {
  key: string;
  stt: number;
  ma?: string;
  ten?: string;
  kieu_chon?: string;
  bat_buoc?: string;
  do_rong?: string;
  trang_thai?: string;
  cau_tra_loi?: string;
  dap_an?: string | null;
}

export type TableDanhGiaSucKhoeColumnDataIndex = keyof TableDanhGiaSucKhoeColumnDataType;

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const tableDanhGiaSucKhoeColumns: TableProps<TableDanhGiaSucKhoeColumnDataType>["columns"] = [
  {
    ...defaultTableColumnsProps,
    title: "STT",
    dataIndex: "stt",
    key: "stt",
    width: 60,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Mã câu hỏi",
    dataIndex: "ma",
    key: "ma",
    width: 120,
    align: "center",
  },
  {
    ...defaultTableColumnsProps,
    title: "Nội dung câu hỏi",
    dataIndex: "ten",
    key: "ten",
    width: 400,
    align: "left",
    onCell: () => ({
      className: "!py-1 text-[11px]",
      style: {whiteSpace: "pre-wrap", wordWrap: "break-word"},
    }),
    render: (text: any, record: any) => {
      if (record.key && record.key.toString().includes("empty")) return "";
      const isBatBuoc = record.bat_buoc === "C";
      return (
        <div style={{whiteSpace: "pre-wrap", wordWrap: "break-word", lineHeight: "1.4"}}>
          <span>
            {isBatBuoc && <span style={{color: "#F53D5C"}}>* </span>}
            {text?.toString() || ""}
          </span>
        </div>
      );
    },
  },
  {
    ...defaultTableColumnsProps,
    title: "Câu trả lời",
    dataIndex: "cau_tra_loi",
    key: "cau_tra_loi",
    width: 200,
    align: "center",
  },
];

/* THÊM HỢP ĐỒNG BƯỚC 5 */
export interface ThemHopDongStep5Props {
  onDownloadPDF?: (downloadFn: () => void, hasFile: boolean) => void;
}

export interface IThemHopDongStep5Ref {}
