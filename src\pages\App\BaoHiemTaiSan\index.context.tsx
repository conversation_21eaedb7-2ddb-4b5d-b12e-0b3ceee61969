import {createContext, useContext} from "react";
import {BaoHiemTaiSanContextProps} from "./index.model";
//createContext : khai báo 1 context mà component có thể cung cấp hoặc đọc
export const BaoHiemTaiSanContext = createContext<BaoHiemTaiSanContextProps>({
  //khởi tạo các giá trị mặc định, trong context này có giá trị là onSubmit
  danhSachHopDongBaoHiemTaiSan: [],
  danhSachKhachHang: [],
  loading: false,
  tongSoDong: 0,
  defaultFormValue: {},
  listDoiTac: [],
  listSanPham: [],
  listChiNhanh: [],
  listPhongBan: [],
  listPhuongThucKhaiThac: [],
  listChuongTrinhBaoHiem: [],
  listDonViBoiThuong: [],
  timKiemPhanTrangHopDongBaoHiemTaiSan: () => Promise.resolve({data: [], tong_so_dong: 0}),
});

//useContext(someContext): là React Hook cho phép bạn đọc và subscribe context từ component của mình.
// file .context muốn truy câp context thì sử dụng useLoginContext : const {onSubmit} = useLoginContext();
export const useBaoHiemTaiSanContext = () => useContext(BaoHiemTaiSanContext);
