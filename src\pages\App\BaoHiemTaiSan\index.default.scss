#BAO_HIEM_TAI_SAN {
  .ant-table-row:hover td {
    background-color: #e8f5e9 !important; //test
  }

  .header-cell-custom {
    background-color: #96bf49 !important;
    // background: linear-gradient(to right, #96bf49, #009a55) !important;
    color: #fff !important;
    font-weight: bold !important;
    text-align: center !important;
    border-radius: 0 !important;
  }

  /* 🔒 Ẩn cả thanh cuộn dọc và ngang */
  .antd-table-hide-scroll .ant-table-body {
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
  }

  .ant-table-body {
    overflow-y: hidden !important;
  }

  //cố định height cho bảng
  // .ant-table-container {
  //     min-height: 620px;
  // }
  .ant-table-header {
    overflow-y: hidden !important;
  }
}

.modal-import-excel .ant-table-row:hover td {
  background-color: transparent !important; //test
}

.custom-step-container {
  display: flex;
  align-items: center;

  .step {
    display: flex;
    align-items: center;
    padding: 6px 12px;
    background: #f5f5f5;
    color: #000;
    border-radius: 4px;
    position: relative;
    font-size: 14px;
    margin-right: 8px;

    svg {
      margin-right: 6px;
    }

    &.active {
      background: #8bc34a;
      color: #fff;
    }

    &.active .arrow-right {
      border-left: 10px solid #8bc34a;
    }

    .arrow-right {
      position: absolute;
      right: -10px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-top: 14px solid transparent;
      border-bottom: 14px solid transparent;
      border-left: 10px solid #f5f5f5;
    }
  }
}

.header-cell-custom {
  background-color: #96bf49 !important;
  // background: linear-gradient(to right, #96bf49, #009a55) !important;
  color: #fff !important;
  font-weight: bold !important;
  text-align: center !important;
  border-radius: 0 !important;
}

.table-row-active {
  background-color: #96bf49 !important;
  // transition: background 0.3s;
}

.ant-table-row:hover td {
  background-color: #e8f5e9 !important; //test
}

.ant-form-item .ant-form-item-label {
  padding-bottom: 0px !important;

  label {
    font-size: 12px !important;
    font-weight: 600 !important;
  }
}

.custom-table-title-padding .ant-table-title {
  padding: 8px 8px !important;
}

.custom-full-modal .custom-table .ant-table-body {
  overflow-y: hidden !important;
}

.custom-full-modal .custom-table .ant-table-header {
  overflow-y: hidden !important;
  border-radius: 0 !important;
}

.hide-scrollbar .ant-table-body {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
}

.hide-scrollbar .ant-table-body::-webkit-scrollbar {
  display: none;
  /* Chrome/Safari/Webkit */
}

.no-header-border-radius .ant-table-thead > tr > th {
  border-radius: 0 !important;
}

.ant-table-wrapper .ant-table .ant-table-header {
  border-radius: 0 !important;
}

.custom-link-button.ant-btn-link {
  height: auto !important;
}

.custom-link-button.button_xoa.ant-btn-link:hover {
  color: darkred !important;
}
.custom-link-button.button_xoa.ant-btn-link {
  color: #ff4d4f !important;
  height: auto !important;
}

//style riêng cho rowspan
.table-row-hover-merge > td,
.table-no-style-hover .ant-table-tbody > tr.table-row-hover-merge > td {
  background: #e8f5e9 !important;
  transition: background 0.2s;
}

.hide-scrollbar .ant-table-body {
  border-bottom: 1px solid #f0f0f0;
}

.no-hover-table .ant-table-tbody > tr.ant-table-row:hover > td {
  background: none !important;
}

.ant-list .ant-list-item {
  padding: 4px 0 !important;
}

.ant-popover .ant-popover-content {
  position: relative;
  margin-bottom: 40px !important;
}
.custom-full-modal .ant-form-item .ant-form-item-control-input {
  min-height: 22px;
}
.fix-textarea-height textarea.ant-input {
  height: 60px !important;
  min-height: 0 !important;
}
