import {useMutation} from "react-query";
import {CommonEndpoint} from "../axios";
import {ReactQuery} from "@src/@types";

/*
==== THAM SỐ REQUEST ====
- ILayDanhSachNguoiDungTheoNhomParams : LAY_DANH_SACH_MENU_NGUOI_DUNG_THEO_NHOM
- ILayDanhSachDoiTacParams : TIM_KIEM_DANH_SACH_DOI_TAC_PHAN_TRANG
- actionCode : api nào cũng phải truyền actionCode lên để xử lý trong before request
 */
type CommonExecuteParams = ReactQuery.IPhanTrang & {
  actionCode: string;
} & ReactQuery.ILayDanhSachNguoiDungTheoNhomParams &
  ReactQuery.ILayDanhSachDoiTacParams &
  ReactQuery.IUpdatePhongBanParams &
  ReactQuery.IChiTiethDoiTacParams &
  ReactQuery.ILayDanhSachPhongBanPhanTrangParams &
  ReactQuery.IUpdateDoiTacParams &
  ReactQuery.ILayDanhSachDonViChiNhanhParams &
  ReactQuery.IChiTietDonViChiNhanhParams &
  ReactQuery.ILayDanhSachTaiKhoanNguoiDungPhanTrangParams &
  ReactQuery.IChiTietChucNangParams &
  ReactQuery.ILayDanhSachChucNangPhanTrangParams &
  ReactQuery.IUpdateChucNangParams &
  ReactQuery.ILayDanhSachChucDanhPhanTrangParams &
  ReactQuery.ILayDanhSachKhachHangPhanTrangParams &
  ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams &
  ReactQuery.ILietKeSanPhamParams &
  ReactQuery.ILietKeChuongTrinhBaoHiemParams &
  ReactQuery.ITimKiemPhanTrangPhuongThucKhaiThacParams &
  ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams &
  ReactQuery.IChiTietDanhMucDaiLyParams &
  ReactQuery.IUpdateDaiLyParams &
  ReactQuery.IlayChiTietPhuongThucKhaiThacParams &
  ReactQuery.IUpdateDaiLyParams &
  ReactQuery.IlayChiTietPhuongThucKhaiThacParams &
  ReactQuery.IUpdatePhuongThucKhaiThacParams &
  ReactQuery.ITimKiemPhanTrangDanhSachSanPhamParams &
  ReactQuery.IUpdateDanhMucSanPhamParams &
  ReactQuery.IChiTietDanhMucSanPhamParams &
  ReactQuery.IUpdateHopDongParams &
  ReactQuery.ILayDanhSachHopDongConNguoiPhanTrangParams &
  ReactQuery.ILietKeDanhSachDaiLyParams &
  ReactQuery.ILietKeDonViBoiThuongTPAParams &
  ReactQuery.IChiTietChucDanhParams &
  ReactQuery.ILietKePhuongThucKhaiThacParams &
  ReactQuery.ITimKiemPhanTrangBoMaQuyenLoiParams &
  ReactQuery.IChiTietBoMaQuyenLoiParams &
  ReactQuery.IUpdateBoMaQuyenLoiParams &
  ReactQuery.IChiTietDoiTuongBaoHiemXeParams &
  ReactQuery.IChiTietHeThongMenuParams &
  ReactQuery.ICapNhatHeThongMenuParams &
  ReactQuery.ITimkiemPhanTrangHeThongMenuParams &
  ReactQuery.ITimKiemPhanTrangBoMaNguyenTeParams &
  ReactQuery.IlayChiTietBoMaNguyenTeParams &
  ReactQuery.IUpdateBoMaNguyenTeParams &
  ReactQuery.ITimKiemPhanTrangDanhMucNganHangParams &
  ReactQuery.ICapNhatDanhMucNganHangParams &
  ReactQuery.IChiTietDanhMucNganHangParams &
  ReactQuery.IChiTietChuongTrinhBaoHiemParams &
  ReactQuery.ICapNhatChuongTrinhBaoHiemParams &
  ReactQuery.ITimKiemPhanTrangChuongTrinhBaoHiemParams &
  ReactQuery.IXoaGoiBaoHiemKhoiCTBHParams &
  ReactQuery.ICapNhatDanhMucNganHangParams &
  ReactQuery.ITimKiemPhanTrangGoiBaoHiemParams &
  ReactQuery.ICapNhatGoiBaoHiemParams &
  ReactQuery.IChiTietGoiBaoHiemParams &
  ReactQuery.ITimKiemPhanTrangChiNhanhNganHangParams & //NXH
  ReactQuery.IChiTietChiNhanhNganHangParams & //NXH
  ReactQuery.ICapNhatChiNhanhNganHangParams & //NXH
  ReactQuery.ITimKiemPhanTrangDanhMucLoaiXeParams & //NXH
  ReactQuery.ICapNhatDanhMucLoaiXeParams & //NXH
  ReactQuery.IChiTietDanhMucLoaiXeParams & //NXH
  ReactQuery.ITimKiemPhanTrangNhomHangMucXeParams & //NXH
  ReactQuery.IChiTietNhomHangMucXeParams & //NXH
  ReactQuery.ICapNhatNhomHangMucXeParams & //NXH
  ReactQuery.ITimKiemPhanTrangHangMucXeParams & //NXH
  ReactQuery.IChiTietHangMucXeParams & //NXH
  ReactQuery.ICapNhatHangMucXeParams & //NXH
  ReactQuery.ITimKiemPhanTrangMucDoTonThatXeParams & //NXH
  ReactQuery.IChiTietMucDoTonThatXeParams & //NXH
  ReactQuery.ICapNhatMucDoTonThatXeParams & //NXH
  ReactQuery.IChiTietGoiBaoHiemParams &
  ReactQuery.IUpdateKyThanhToanParams &
  ReactQuery.IChiTietDoiTuongBaoHiemXeParams &
  ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams &
  ReactQuery.ILayThongTinThanhToanCuaHopDongBaoHiemParams &
  ReactQuery.IChiTietKyThanhToanParams &
  //ReactQuery.ILayThongTinDongCuaHopDongBaoHiemParams &
  ReactQuery.IChiTietThongTinCauHinhDongBHParams &
  ReactQuery.ITimKiemPhanTrangDanhMucTinhThanhParams & // TINH THANH
  ReactQuery.IChiTietDanhMucTinhThanhParams & // TINH THANH
  ReactQuery.ICapNhatDanhMucTinhThanhParams & // TINH THANH
  ReactQuery.ITimKiemPhanTrangDanhMucQuanHuyenParams & // QUẬN HUYỆN
  ReactQuery.IChiTietDanhMucQuanHuyenParams & // QUẬN HUYỆN
  ReactQuery.ICapNhatDanhMucQuanHuyenParams & // QUẬN HUYỆN
  ReactQuery.ITimKiemPhanTrangDanhMucPhuongXaParams & // PHƯỜNG XÃ
  ReactQuery.IChiTietDanhMucPhuongXaParams & // PHƯỜNG XÃ
  ReactQuery.ICapNhatDanhMucPhuongXaParams & // PHƯỜNG XÃ
  ReactQuery.ITimKiemPhanTrangDanhMucChauLucParams & // CHÂU LỤC
  ReactQuery.IChiTietDanhMucChauLucParams & // CHÂU LỤC
  ReactQuery.ICapNhatDanhMucChauLucParams & // CHÂU LỤC
  ReactQuery.ITimKiemPhanTrangDanhMucKhuVucParams & // KHU VỰC
  ReactQuery.IChiTietDanhMucKhuVucParams & // KHU VỰC
  ReactQuery.ICapNhatDanhMucKhuVucParams & // KHU VỰC
  ReactQuery.ITimKiemPhanTrangDanhMucQuocGiaParams & // QUỐC GIA
  ReactQuery.IChiTietDanhMucQuocGiaParams & // QUỐC GIA
  ReactQuery.ICapNhatDanhMucQuocGiaParams & // QUỐC GIA
  ReactQuery.ITimKiemPhanTrangDanhMucBenhVienParams & // DANH MỤC BỆNH VIỆN
  ReactQuery.IChiTietDanhMucBenhVienParams & // DANH MỤC BỆNH VIỆN
  ReactQuery.ICapNhatDanhMucBenhVienParams & // DANH MỤC BỆNH VIỆN
  ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams &
  ReactQuery.IChiTietThongTinCauHinhDongBHParams &
  ReactQuery.IUpdateCauHinhDongBHParams &
  ReactQuery.IChiTietThongTinCauHinhTaiBHParams &
  ReactQuery.IUpdateCauHinhTaiBHParams &
  ReactQuery.IUpdateDoiTuongApDungDongBHParams &
  ReactQuery.ILietKeDanhSachNguoiDuyetParams &
  ReactQuery.ITrinhPheDuyetHopDongParams &
  ReactQuery.ITimKiemPhanTrangHopDongTrinhDuyetParams &
  ReactQuery.IUpdateDoiTuongApDungTaiBHParams &
  ReactQuery.ITimKiemPhanTrangNguoiDuocBaoHiemHopDongConNguoi &
  ReactQuery.IChiTietNguoiDuocBaoHiemHopDongConNguoi &
  ReactQuery.ICapNhatThongTinNguoiDuocBaoHiemHopDongConNguoi &
  ReactQuery.IXemChiTietHopDongTrinhDuyetParams &
  ReactQuery.ICapNhatHopDongTrinhDuyetParams &
  ReactQuery.ITimKiemPhanTrangCauHinhMauHopDongParams &
  ReactQuery.IChiTietCauHinhMauHopDongParams &
  ReactQuery.ICapNhatCauHinhMauHopDongParams &
  ReactQuery.ITimKiemPhanTrangCauHinhMauGCNParams &
  ReactQuery.IChiTietCauHinhMauGCNParams &
  ReactQuery.ICapNhatCauHinhMauGCNParams &
  ReactQuery.IGetFileThumbnailParams &
  ReactQuery.IUploadFileTheoDoiTuongXeParams &
  ReactQuery.ILietKeHangMucXeTheoNhomParams &
  ReactQuery.ILuuHangMucTonThatParams &
  ReactQuery.ILuuDanhGiaTonThatParams &
  ReactQuery.ILietKeDieuKhoanNguoiDuocBaoHiemParams &
  ReactQuery.ILuuDieuKhoanNguoiDuocBaoHiemParams &
  ReactQuery.ILietKeDieuKhoanBoSungNguoiDuocBaoHiemParams &
  ReactQuery.ILuuDieuKhoanBoSungNguoiDuocBaoHiemParams &
  ReactQuery.ILietKeDanhSachNguoiPhuThuocHopDongConNguoiParams &
  ReactQuery.IChiTietNguoiPhuThuocHopDongConNguoiParams &
  ReactQuery.ICapNhatNguoiPhuThuocHopDongConNguoiParams &
  ReactQuery.ITimKiemPhanTrangDanhSachNhomPhanCapDuyetParams &
  ReactQuery.IChiTietNhomPhanCapDuyetParams &
  ReactQuery.IUpdateNhomPhanCapDuyetParams &
  ReactQuery.IUpdateTaiKhoanNguoiDungParams &
  ReactQuery.IUpdateCauHinhPhanCapPheDuyetParams &
  ReactQuery.IChiTietCauHinhPhanCapPheDuyetCTParams &
  ReactQuery.IUpdateCauHinhPhanCapPheDuyetCTParams &
  ReactQuery.ILietKeCauHinhPhanCapPheDuyetCTParams &
  ReactQuery.IDeleteCauHinhPhanCapPheDuyetCTParams &
  ReactQuery.ILietKeCauHinhPhanCapPheDuyetParams &
  ReactQuery.ILietKeCauHoiApDungParams &
  ReactQuery.IDeleteCauHoiApDungParams &
  ReactQuery.ILietKeCauHoiParams &
  ReactQuery.IChiTietCauHoiParams &
  ReactQuery.IUpdateCauHoiParams &
  ReactQuery.IDeleteCauHoiParams &
  ReactQuery.ICapNhatNguoiPhuThuocHopDongConNguoiParams &
  ReactQuery.IExportExcelParams &
  ReactQuery.ILietKeDanhSachTyLeHoaHongParams &
  ReactQuery.ICapNhatCauHinhTyLeHoaHongParams &
  ReactQuery.ILayCayTyLeHoaHongParams &
  ReactQuery.ITimKiemPhanTrangDanhSachChucNangTheoVaiTroParams &
  ReactQuery.IChiTietChucNangTheoVaiTroParams &
  ReactQuery.ICapNhatChucNangTheoVaiTroParams &
  ReactQuery.IDeleteChucNangTheoVaiTroParams &
  ReactQuery.ILayDanhSachNhomChucNangPhanTrangParams &
  ReactQuery.IChiTietNhomChucNangParams &
  ReactQuery.IUpdateNhomChucNangParams &
  ReactQuery.IDeleteChucNangTheoNhomParams &
  ReactQuery.ILietKeCauHoiDanhGiaSucKhoeConNguoiParams &
  ReactQuery.ILuuDanhGiaSucKhoeConNguoiParams &
  ReactQuery.ITimKiemPhanTrangDanhSachNghiepVuParams &
  ReactQuery.IChiTietDanhMucNghiepVuParams &
  ReactQuery.IUpdateDanhMucNghiepVuParams &
  ReactQuery.ITimKiemPhanTrangNhomDoiTuongParams &
  ReactQuery.IChiTietNhomDoiTuongParams &
  ReactQuery.IUpdateNhomDoiTuongParams;

/* 
==== RESPONSE ====
*/
export type CommonExecuteResponse = {
  output?: {
    tong_so_dong?: number;
    so_hd?: string;
    so_id?: number;
    id?: number;
  };
  data: Array<CommonExecute.Execute.IMenuNguoiDungTheoNhom> &
    Array<CommonExecute.Execute.IPhongBan> & {data: Array<CommonExecute.Execute.IDoiTac>; tong_so_dong: number} & CommonExecute.Execute.IPhongBan &
    Array<CommonExecute.Execute.IChiNhanh> & {data: CommonExecute.Execute.IDoiTac} & {data: Array<CommonExecute.Execute.IDanhSachPhongBanPhanTrang>; tong_so_dong: number} & {
      data: Array<CommonExecute.Execute.IChiNhanh>;
      tong_so_dong: number;
    } & {data: CommonExecute.Execute.IChiNhanh} & {data: Array<CommonExecute.Execute.IDanhSachTaiKhoanNguoiDungPhanTrang>; tong_so_dong: number} & CommonExecute.Execute.IChiTietTaiKhoanNguoiDung &
    CommonExecute.Execute.IChiTietChucNang & {data: Array<CommonExecute.Execute.IChiTietChucDanh>; tong_so_dong: number} & {data: Array<CommonExecute.Execute.IKhachHang>; tong_so_dong: number} & {
      data: Array<CommonExecute.Execute.IDoiTacNhanVien>;
      tong_so_dong: number;
    } & {data: Array<CommonExecute.Execute.ISanPham>} & {data: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>} & {data: Array<CommonExecute.Execute.INhaBaoHiemTPA>} & {
      data: Array<CommonExecute.Execute.IDanhMucDaiLy>;
      tong_so_dong: number;
    } & {data: Array<CommonExecute.Execute.IChiTietDanhMucDaiLy>} & {data: Array<CommonExecute.Execute.IDanhSachPhuongThucKhaiThac>; tong_so_dong: number} & {
      data: Array<CommonExecute.Execute.IChiTietPhuongThucKhaiThac>;
    } & {data: Array<CommonExecute.Execute.IDanhMucSanPham>; tong_so_dong: number} & {data: Array<CommonExecute.Execute.IHopDongConNguoi>; tong_so_dong: number} & {
      data: Array<CommonExecute.Execute.IDanhSachBoMaQuyenLoi>;
      tong_so_dong: number;
    } & CommonExecute.Execute.IChiTietBoMaQuyenLoi & {data: number} & number &
    CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi & {data: Array<CommonExecute.Execute.IHeThongMenu>; tong_so_dong: number} & {
      data: Array<CommonExecute.Execute.IDanhSachBoMaNguyenTe>;
      tong_so_dong: number;
    } & {data: Array<CommonExecute.Execute.IChiTietBoMaNguyenTe>} & {
      data: Array<CommonExecute.Execute.IDanhMucNganHang>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IGoiBaoHiem>;
    } & {
      data: Array<CommonExecute.Execute.IGoiBaoHiemConNguoi>;
      tong_so_dong: number;
    } & CommonExecute.Execute.IChiTietGoiBaoHiemConNguoi &
    CommonExecute.Execute.IHopDongXe &
    CommonExecute.Execute.IKyThanhToan & {
      data: Array<CommonExecute.Execute.IDanhMucTinhThanh>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucQuanHuyen>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucPhuongXa>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucChauLuc>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucKhuVuc>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucQuocGia>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IDanhMucBenhVien>;
      tong_so_dong: number;
    } & CommonExecute.Execute.IKyThanhToan &
    CommonExecute.Execute.ITaiBaoHiem & {
      data: Array<CommonExecute.Execute.INguoiPheDuyetHopDongXe>;
    } & CommonExecute.Execute.IDongBaoHiem & {
      data: Array<CommonExecute.Execute.INhomHangMucXe>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IHangMucXe>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.ICauHinhMauHopDong>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.ICauHinhMauGCN>;
      tong_so_dong: number;
    } & {
      data: Array<CommonExecute.Execute.IMucDoTonThatXe>;
      tong_so_dong: number;
    } & {data: CommonExecute.Execute.INguoiDuocBaoHiemHopDongConNguoi[]; tong_so_dong: number} & CommonExecute.Execute.INguoiDuocBaoHiemHopDongConNguoi & {
      data: CommonExecute.Execute.IDieuKhoanQuyenLoiConNguoi[];
    } & {data: CommonExecute.Execute.IChiTietNguoiPhuThuoc[]} & CommonExecute.Execute.IChiTietNguoiPhuThuoc & {
      data: Array<CommonExecute.Execute.INhomPhanCapDuyet>;
      tong_so_dong: number;
    } & CommonExecute.Execute.INhomPhanCapDuyet & {
      data: Array<CommonExecute.Execute.ICauHinhPhanCapPheDuyet>;
      tong_so_dong: number;
    } & {data: Array<CommonExecute.Execute.ICauHinhPhanCapPheDuyetCT>} & {data: Array<CommonExecute.Execute.ICauHoiApDung>} & {data: Array<CommonExecute.Execute.ICauHoi>} & {
      data: Array<CommonExecute.Execute.IChiTietTaiKhoanNguoiDung> & {data: Array<CommonExecute.Execute.IChiTietNguoiSuDung>};
    } & {data: Array<CommonExecute.Execute.ICauHinhTyLeHoaHong>} & {data: Array<CommonExecute.Execute.ICayTyLeHoaHong>} & {data: Array<CommonExecute.Execute.IChucNangTheoVaiTro>} & {
      data: Array<CommonExecute.Execute.IChiTietChucNangTheoVaiTro>;
    } & {data: Array<CommonExecute.Execute.INhomChucNang>} & {data: Array<CommonExecute.Execute.IChiTietNhomChucNang>} & {data: Array<CommonExecute.Execute.IDanhSachNhomChucNangPhanTrang>} & {
      data: Array<CommonExecute.Execute.IVaiTroChucNang>;
    } & {data: Array<CommonExecute.Execute.IDanhMucNghiepVu>; tong_so_dong: number} & {data: Array<CommonExecute.Execute.INhomDoiTuong>; tong_so_dong: number};
};

// Tên react hook phải bắt đầu bằng chữ use
export const useCommonExecute = () => {
  //type của useMutation<ReturnType, ErrorType, InputType>()
  return useMutation<CommonExecuteResponse, any, CommonExecuteParams>({
    mutationFn: CommonEndpoint.getCommonExecute, //mutationFn function : Đây là hàm thực sự gọi API từ axios
  });
};
