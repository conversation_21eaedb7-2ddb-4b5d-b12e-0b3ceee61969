import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface BaoHiemTaiSanContextProps {
  danhSachHopDongBaoHiemTaiSan: Array<CommonExecute.Execute.IBaoHiemTaiSan>;
  danhSachKhachHang: Array<CommonExecute.Execute.IKhachHang>;
  loading: boolean;

  tongSoDong: number;
  defaultFormValue: object;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listSanPham: Array<CommonExecute.Execute.ISanPham>;
  listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>;
  listPhongBan: Array<CommonExecute.Execute.IPhongBan>;
  listPhuongThucKhaiThac: Array<CommonExecute.Execute.IPhuongThucKhaiThac>;
  listChuongTrinhBaoHiem: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>;
  listDonViBoiThuong: Array<CommonExecute.Execute.INhaBaoHiemTPA>;
  timKiemPhanTrangHopDongBaoHiemTaiSan: (params: ReactQuery.ITimKiemPhanTrangHopDongBaoHiemTaiSanParams) => void;
}
