import {ReactQuery} from "@src/@types";

// khai báo interface props Context của Login
export interface BaoHiemTaiSanContextProps {
  danhSachHopDongBaoHiemTaiSan: Array<CommonExecute.Execute.IBaoHiemTaiSan>;
  danhSachKhachHang: Array<CommonExecute.Execute.IKhachHang>;
  loading: boolean;

  tongSoDong: number;
  defaultFormValue: object;
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  listSanPham: Array<CommonExecute.Execute.ISanPham>;
  listChiNhanh: Array<CommonExecute.Execute.IChiNhanh>;
  listPhongBan: Array<CommonExecute.Execute.IPhongBan>;
  listPhuongThucKhaiThac: Array<CommonExecute.Execute.IPhuongThucKhaiThac>;
  listChuongTrinhBaoHiem: Array<CommonExecute.Execute.IChuongTrinhBaoHiem>;
  listDonViBoiThuong: Array<CommonExecute.Execute.INhaBaoHiemTPA>;
  filterParams: ReactQuery.ITimKiemPhanTrangHopDongBaoHiemTaiSanParams & ReactQuery.IPhanTrang;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ITimKiemPhanTrangHopDongBaoHiemTaiSanParams & ReactQuery.IPhanTrang>>;

  timKiemPhanTrangHopDongBaoHiemTaiSan: (params: ReactQuery.ITimKiemPhanTrangHopDongBaoHiemTaiSanParams) => void;
}
