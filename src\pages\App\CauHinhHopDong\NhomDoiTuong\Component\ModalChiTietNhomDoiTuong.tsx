import {ArrowLeftOutlined, CheckOutlined, CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal, Highlighter, InputCellTable, TableFilterDropdown} from "@src/components";
import {Col, Flex, Form, InputRef, Modal, Row, Table, TableColumnType} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useNhomDoiTuongContext} from "../index.context";
import {
  FormChiTietNhomDoiTuong,
  INhomDoiTuongSelected,
  IModalChiTietNhomDoiTuongRef,
  IModalThemNhomDoiTuongChaRef,
  ChiTietNhomDoiTuongProps,
  TableDoiTuongCTDataType,
  DataIndexDoiTuongCT,
  doiTuongCTColumns,
  KIEU_DU_LIEU,
} from "./index.configs";
import {TRANG_THAI} from "../index.configs";
import {ModalThemNhomDoiTuongCha} from "./ModalThemNhomDoiTuongCha";
import "../index.default.scss";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {FilterDropdownProps} from "antd/es/table/interface";
const {ma, ten, ma_cha, ma_dau, mo_ta, stt, trang_thai, trang_thai_ten} = FormChiTietNhomDoiTuong;

const ModalChiTietNhomDoiTuongComponent = forwardRef<IModalChiTietNhomDoiTuongRef, ChiTietNhomDoiTuongProps>(({}: ChiTietNhomDoiTuongProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataNhomDoiTuong?: CommonExecute.Execute.INhomDoiTuong) => {
      setIsOpen(true);
      if (dataNhomDoiTuong) setDisableSubmit(true);
      if (dataNhomDoiTuong) setchitietNhomDoiTuong(dataNhomDoiTuong);
    },

    close: () => setIsOpen(false),
  }));

  const [chitietNhomDoiTuong, setchitietNhomDoiTuong] = useState<CommonExecute.Execute.INhomDoiTuong | null>(null);
  const refModelThemNhomDoiTuongCha = useRef<IModalThemNhomDoiTuongChaRef>(null);
  const [isOpen, setIsOpen] = useState(false);
  const {loading, onUpdateNhomDoiTuong, filterParams, setFilterParams, danhSachNhomDoiTuong, layDanhSachNhomDoiTuongPhanTrang, listNhomDoiTuongCT, layChiTietNhomDoiTuong} = useNhomDoiTuongContext();
  const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const formValues = Form.useWatch([], form);
  const [nhomDoiTuongSelected, setNhomDoiTuongSelected] = useState<INhomDoiTuongSelected | null>(null);
  const [dataSource, setDataSource] = useState<Array<TableDoiTuongCTDataType>>([]);
  const [pageSize, setPageSize] = useState(4);
  const [inputRowKeys, setInputRowKeys] = useState<string[]>([]);
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [inputRowKey, setInputRowKey] = useState<string | null>(null);
  useEffect(() => {
    console.log(" dataSource", dataSource);
    console.log("nhóm đối tượng chi tiết ", listNhomDoiTuongCT);
  }, [dataSource, listNhomDoiTuongCT]);
  // init form data
  useEffect(() => {
    if (chitietNhomDoiTuong) {
      const arrFormData = [];
      for (const key in chitietNhomDoiTuong) {
        arrFormData.push({
          name: key,
          value: chitietNhomDoiTuong[key as keyof CommonExecute.Execute.INhomDoiTuong],
        });
      }
      const nhomDoiTuongCha = danhSachNhomDoiTuong.find(nhomDoiTuong => nhomDoiTuong.ma === chitietNhomDoiTuong.ma_cha);
      if (nhomDoiTuongCha) {
        setNhomDoiTuongSelected({
          ma: nhomDoiTuongCha.ma,
          ten: nhomDoiTuongCha.ten,
        });
      } else {
        setNhomDoiTuongSelected(null);
      }
      form.setFields(arrFormData);
    }
  }, [chitietNhomDoiTuong]);

  //xử lý validate form
  useEffect(() => {
    form
      .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
      .then(() => {
        setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
      })
      .catch(() => {
        setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
      });
  }, [form, formValues]);
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setchitietNhomDoiTuong(null);
    form.resetFields();
    setFilterParams(filterParams);
    layDanhSachNhomDoiTuongPhanTrang(filterParams);
  }, [filterParams, form]);

  //Bấm Update
  const onConfirm = async () => {
    try {
      const values: ReactQuery.IUpdateNhomDoiTuongParams = form.getFieldsValue(); //lấy ra values của form
      console.log("values", values);
      //với trường hợp tạo mới -> check mã đối tác đã tồn tại
      if (!chitietNhomDoiTuong) {
        // console.log("check list dai ly", listNhomDoiTuong);
        // for (let i = 0; i < listNhomDoiTuong.length; i++) {
        //   if (listNhomDoiTuong[i].ma === values.ma) {
        //     form.setFields([
        //       {
        //         name: "ma",
        //         errors: ["Mã đại lý đã tổn tại!"],
        //       },
        //     ]);
        //     return;
        //   }
        // }
      }

      const validRows = dataSource.filter(row => !row.key.includes("empty") && row.ma_thuoc_tinh && row.ma_thuoc_tinh.trim() !== "");

      const finalValues = {
        ...values,
        dt_ct: validRows.map(row => ({
          ma_thuoc_tinh: row.ma_thuoc_tinh,
          ten_thuoc_tinh: row.ten_thuoc_tinh,
          kieu_dl: row.kieu_dl,
        })),
      };
      console.log("paraarram ", finalValues);
      const response = await onUpdateNhomDoiTuong(finalValues);
      console.log("check respon ", response);
      if (response === -1) {
        console.log("cập nhật thành công");
        // closeModal();
        layChiTietNhomDoiTuong({ma: values.ma});
      } else {
        console.log("cập nhật thất bại");
      }
      //cập nhật lại danh mục đại lý
    } catch (error) {
      console.log("onConfirm", error);
    }
  };
  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexDoiTuongCT) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);
  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexDoiTuongCT) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  const handleAddRow = () => {
    // Tìm index dòng trống đầu tiên
    const emptyRowIndex = dataSource.findIndex(item => item.key.includes("empty"));
    const newData = [...dataSource];
    if (emptyRowIndex !== -1) {
      newData.splice(emptyRowIndex, 1);
    }
    // Tìm vị trí cuối cùng của dữ liệu gốc (dòng có ma_cau_hoi hoặc trường phân biệt)
    const lastDataIndex = newData.reduce((lastIdx, item, idx) => (item.ma_thuoc_tinh ? idx : lastIdx), -1);
    // Tạo dòng dữ liệu mới trống
    const newKey = `new-${Date.now()}`;
    const newRow = {
      key: newKey,
      ma_thuoc_tinh: "",
      ten_thuoc_tinh: "",
      kieu_dl: "",
    };

    // Thêm dòng mới vào ngay sau dòng dữ liệu cuối cùng
    newData.splice(lastDataIndex + 1, 0, newRow);

    setDataSource(newData);
    setInputRowKey(newKey);
    setInputRowKeys(prev => [...prev, newKey]);
  };
  const handleInputChange = (index: number, dataIndex: string, value: string) => {
    setDataSource(prev => {
      const next = [...prev];
      next[index] = {...next[index], [dataIndex]: value};

      return next;
    });
  };
  const getColumnSearchDoiTuongCTProps = (dataIndex: DataIndexDoiTuongCT, title: string): TableColumnType<DataIndexDoiTuongCT> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInput}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearch}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleReset}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex as keyof DataIndexDoiTuongCT]
        ? record[dataIndex as keyof DataIndexDoiTuongCT]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    // filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomTable : undefined,
    render: (text, record, index) => {
      //   if (dataIndex === "trang_thai_ten") {
      //     // Xác định màu dựa vào text (trạng thái hiển thị)
      //     const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
      //     if (record.key.toString().includes("empty")) return "";
      //     return <Tag color={color}>{text}</Tag>;
      //   }
      // if (record.key.toString().includes("empty")) return <div style={{height: 20}} />;

      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Button type="link" className="h-auto p-0" style={{color: "transparent"}} icon={<CloseOutlined style={{visibility: "hidden"}} />}></Button>
      );
    },
  });
  const renderColumn = (column: any, index: number) => {
    if (column.dataIndex === "ma_thuoc_tinh" || column.dataIndex === "ten_thuoc_tinh") {
      return {
        ...column,
        render: (_: any, record: TableDoiTuongCTDataType, index: number) => {
          const isEmptyRow = record.key.includes("empty");
          // Nếu là dòng trống đang được chọn để nhập liệu
          if (inputRowKeys.includes(record.key)) {
            return (
              <div className="custom-checkbox-cell">
                <InputCellTable
                  className=""
                  component="input"
                  key={`${record.key}-${column.dataIndex}`}
                  value={record[column.dataIndex as keyof TableDoiTuongCTDataType] ?? ""}
                  index={index}
                  dataIndex={column.dataIndex}
                  onChange={handleInputChange}
                  autoFocus
                />
                {/* <span className="ml-1">%</span> */}
              </div>
            );
          }
          // Các dòng trống khác chỉ để trống
          // if (isEmptyRow) return <div style={{height: 22}} />;
          // Dòng thường thì render như cũ
          if (isEmptyRow) {
            return (
              <div className="custom-checkbox-cell">
                <FormInput className="!mb-0" component="select" disabled style={{display: "none"}} />
              </div>
            );
          }
          return (
            <div className="custom-checkbox-cell">
              <InputCellTable
                className=""
                component="input"
                key={`${record.key}-${column.dataIndex}`}
                value={record[column.dataIndex as keyof TableDoiTuongCTDataType] ?? ""}
                index={index}
                dataIndex={column.dataIndex}
                onChange={handleInputChange}
                autoFocus
              />
              {/* <span className="ml-1">%</span> */}
            </div>
          );
        },
      };
    }
    if (column.dataIndex === "kieu_dl") {
      return {
        ...column,
        render: (text: any, record: TableDoiTuongCTDataType, index: number) => {
          const isEmptyRow = record.key.includes("empty");

          // Trường hợp dòng trống: hiển thị ô trống
          if (isEmptyRow) {
            return "";
          }
          // const value = text ? dayjs(text, "DD/MM/YYYY") : undefined;
          // Trường hợp dòng có dữ liệu: hiển thị date-picker
          return (
            <FormInput
              className="!mb-0"
              component="select"
              value={record[column.dataIndex as keyof TableDoiTuongCTDataType] ?? ""}
              options={KIEU_DU_LIEU}
              onChange={(value: string) => {
                handleInputChange(index, "kieu_dl", value);
              }}
              // placeholder={"Chọn kiểu dữ liệu"}
            />
          );
        },
      };
    }
    return {
      ...column,
      ...(column.key && typeof column.title === "string" && column.key !== "stt" ? getColumnSearchDoiTuongCTProps(column.key, column.title) : {}),
      render: (text: string) => text || "", // Đảm bảo hiển thị chuỗi rỗng nếu text undefined
    };
  };
  const dataTableListDoiTuongCT = useMemo<Array<TableDoiTuongCTDataType>>(() => {
    try {
      const mappedData = Array.isArray(listNhomDoiTuongCT)
        ? listNhomDoiTuongCT
            .filter(item => item)
            .map((item: any, index: number) => ({
              key: index.toString(),
              ma_thuoc_tinh: item.ma_thuoc_tinh,
              ten_thuoc_tinh: item.ten_thuoc_tinh,
              kieu_dl: item.kieu_dl,
            }))
        : [];
      console.log("mappedData", mappedData);
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);

      setDataSource([...mappedData, ...arrEmptyRow]);

      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [listNhomDoiTuongCT, pageSize]);

  // RENDER

  //FOOTER
  const renderFooter = () => {
    return (
      <Form.Item>
        <Button
          className="mr-2"
          type="primary"
          icon={<PlusCircleOutlined />}
          onClick={
            handleAddRow
            // () => refModalThemDoiTuongCT.current?.open()
          }
          loading={loading}
          disabled={disableSubmit}>
          Thêm đối tượng chi tiết
        </Button>
        <Button type="default" onClick={() => setIsOpen(false)} className="mr-2" icon={<ArrowLeftOutlined />}>
          Quay lại
        </Button>
        <Button type="primary" disabled={disableSubmit} iconPosition="end" onClick={() => onConfirm()} className="mr-2" icon={<CheckOutlined />}>
          Lưu
        </Button>
      </Form.Item>
    );
  };
  // const renderTableDoiTuongCTFooter = () => {
  //   return (
  //     <Form.Item style={{marginTop: 16, marginBottom: 0, textAlign: "end"}}>
  //       <Button
  //         className=""
  //         type="primary"
  //         icon={<PlusCircleOutlined />}
  //         onClick={
  //           handleAddRow
  //           // () => refModalThemDoiTuongCT.current?.open()
  //         }
  //         loading={loading}
  //         disabled={disableSubmit}>
  //         Thêm đối tượng chi tiết
  //       </Button>
  //     </Form.Item>
  //   );
  // };
  const renderFormInputColum = (props?: any, span = 8) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => (
    <Form form={form} layout="vertical" initialValues={{trang_thai: TRANG_THAI[0].ma}}>
      {/* Row 1: Đội, Mã đại lý, Tên nguồn lực chính đại diện */}
      <Row gutter={16}>
        {/* {renderFormColum({...ma_doi_tac_ql, options: doiTacOptions, disabled: chitietNhomDoiTuong ? true : false})} */}
        {renderFormInputColum({...ma, disabled: chitietNhomDoiTuong ? true : false})}
        {renderFormInputColum({...ten})}
        {renderFormInputColum({...ma_dau, disabled: true})}
      </Row>

      {/* Row 2: Tên đại lý, Loại đại lý, SĐT nguồn lực chính đại diện */}

      {/* Row 4: CMND/CCCD cá nhân, Thứ tự hiển thị */}
      <Row gutter={16}>
        {renderFormInputColum({
          ...ma_cha,
          options: [{ma: nhomDoiTuongSelected?.ma, ten: nhomDoiTuongSelected?.ten}],
          open: false,
          dropdownStyle: {display: "none"},
          labelInValue: true,
          onClick: () => refModelThemNhomDoiTuongCha.current?.open(),
        })}
        {renderFormInputColum({...stt})}
        {renderFormInputColum({...trang_thai, options: TRANG_THAI})}
      </Row>
      <Row gutter={16}>{renderFormInputColum({...mo_ta}, 24)}</Row>
    </Form>
  );
  //renderTable đối tượng chi tiết
  const renderTableDoiTuongCT = () => {
    return (
      <Table<TableDoiTuongCTDataType>
        className="table-doi-tuong-ct no-header-border-radius"
        // className={getNoHoverTableClassName("table-vai-tro no-header-border-radius")}
        {...defaultTableProps}
        style={{cursor: "pointer"}}
        onRow={(record, rowIndex) => {
          return {
            style: {
              cursor: "pointer",
              // background: record.ngay_ad && record.ngay_ad === selectedCauHinhDoiTuongCT ? "#96BF49" : undefined,
            },
            // onClick: () => handleDoiTuongCTRowClick(record), // Xử lý nhấp vào hàng
          };
        }}
        // rowClassName={record => getRowClassName(selectedCauHinhDoiTuongCT === record.ngay_ad)}
        title={null}
        pagination={false}
        columns={(doiTuongCTColumns || []).map(
          renderColumn,
          //   item => {
          //   // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          //   return {
          //     ...item,
          //     ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchDoiTuongCTProps(item.key as keyof TableDoiTuongCTDataType, item.title) : {}),
          //   };
          // }
        )}
        dataSource={dataSource}
        bordered
        scroll={dataSource.length > pageSize ? {y: 165} : undefined}
      />
    );
  };
  //Render
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        className="modal-them-nhom-doi-tuong"
        maskClosable={false}
        title={
          <HeaderModal
            title={chitietNhomDoiTuong ? `Chi tiết nhóm đối tượng ${chitietNhomDoiTuong.ten}` : "Tạo mới nhóm đối tượng"}
            trang_thai_ten={chitietNhomDoiTuong?.trang_thai_ten}
            trang_thai={chitietNhomDoiTuong?.trang_thai}
          />
        }
        // centered
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "60%",
          sm: "60%",
          md: "60%",
          lg: "60%",
          xl: "60%",
          xxl: "60%",
        }}
        style={{top: 10}}
        footer={renderFooter}>
        {renderForm()}
        {renderTableDoiTuongCT()}
        {/* {renderTableDoiTuongCTFooter()} */}
      </Modal>
      <ModalThemNhomDoiTuongCha
        ref={refModelThemNhomDoiTuongCha}
        onSelectNhomDoiTuongCha={nhomDoiTuongCha => {
          console.log("nhomDoiTuongCha from ModalThemNhomDoiTuongCha", nhomDoiTuongCha);
          if (nhomDoiTuongCha) {
            form.setFieldValue("ma_cha", nhomDoiTuongCha.ma);
            setNhomDoiTuongSelected(nhomDoiTuongCha);
          } else {
            form.setFieldValue("ma_cha", null);
          }
        }}
        chiTietNhomDoiTuong={chitietNhomDoiTuong}
      />
    </Flex>
  );
});

ModalChiTietNhomDoiTuongComponent.displayName = "ModalChiTietNhomDoiTuongComponent";
export const ModalChiTietNhomDoiTuong = memo(ModalChiTietNhomDoiTuongComponent, isEqual);
