import {StarFilled, StarOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";
import {LabeledValue} from "antd/es/select";
import dayjs from "dayjs";

const currentDate = dayjs();
const ngayDauNam = dayjs().startOf("year");
export const NGHIEP_VU_XE = "XCG";

export interface TableBaoHiemTaiSanDataType {
  key: number;
  so_id: number;
  stt?: number;
  nv_ten?: string;
  ngay_cap?: string;
  ten_kieu_hd?: string;
  kieu_hd_ten?: string;
  ma_chi_nhanh_ql?: number;
  ma_doi_tac_ql?: string;
  ten_doi_tac_ql: string;
  ten_chi_nhanh_ql: string;
  trang_thai_ten?: string;
  ten_sp?: string;
  tong_phi: number;
  sl_dtuong: number;
}

// ĐỊNH NGHĨA Column RENDER RA TỪNG Ô TRONG CỘT
//đọc https://ant.design/components/table#column để biết thêm về column
export const baoHiemTaiSanColumns: TableProps<TableBaoHiemTaiSanDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott, align: "center"},
  {
    ...defaultTableColumnsProps,
    title: "VIP",
    dataIndex: "vip",
    key: "vip",
    width: 50,
    align: "center",

    render: (text: any, record: any) => {
      if (record.key.toString().includes("empty")) return null;
      if (text) return <StarFilled style={{color: "orange"}} />;
      return <StarOutlined style={{color: "#bfbfbf"}} />;
    },
  },
  {...defaultTableColumnsProps, title: "Nghiệp vụ", dataIndex: "nv_ten", key: "nv_ten", width: 100},
  {...defaultTableColumnsProps, title: "Ngày cấp", dataIndex: "ngay_cap", key: "ngay_cap", width: colWidthByKey.ngay_cap_nhat},
  {...defaultTableColumnsProps, title: "Số hợp đồng", dataIndex: "so_hd", key: "so_hd", width: 250, align: "left"},
  {...defaultTableColumnsProps, title: "Kiểu HĐ", dataIndex: "kieu_hd_ten", key: "kieu_hd_ten", width: 90},
  {...defaultTableColumnsProps, title: "Đối tác cấp đơn", dataIndex: "ten_doi_tac_ql", key: "ten_chi_nhanh_ql", width: 200, align: "center"},
  {...defaultTableColumnsProps, title: "Đơn vị cấp đơn", dataIndex: "ten_chi_nhanh_ql", key: "ten_chi_nhanh_ql", width: 150, align: "center"},
  {...defaultTableColumnsProps, title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130},
  {...defaultTableColumnsProps, title: "Tên sản phẩm", dataIndex: "ten_sp", key: "ten_sp", width: 200, align: "center"},
  {...defaultTableColumnsProps, title: "Tên khách hàng", dataIndex: "ten_kh", key: "ten_kh", width: 250, align: "left"},
  {...defaultTableColumnsProps, title: "Ngày hiệu lực", dataIndex: "ngay_hl", key: "ngay_hl", width: colWidthByKey.ngay_tao},
  {...defaultTableColumnsProps, title: "Ngày kết thúc", dataIndex: "ngay_kt", key: "ngay_kt", width: colWidthByKey.ngay_tao},
  {...defaultTableColumnsProps, title: "SL đối tượng", dataIndex: "sl_dtuong", key: "sl_dtuong", width: 100},
  {...defaultTableColumnsProps, title: "Tổng phí", dataIndex: "tong_phi", key: "tong_phi", width: 100, align: "right"},
  {...defaultTableColumnsProps, title: "Ngày tạo", dataIndex: "ngay_tao", key: "ngay_tao", width: colWidthByKey.ngay_tao},
  {...defaultTableColumnsProps, title: "Người tạo", dataIndex: "nguoi_tao", key: "nguoi_tao", width: colWidthByKey.nguoi_tao},
  {...defaultTableColumnsProps, title: "Ngày cập nhật", dataIndex: "ngay_cap_nhat", key: "ngay_cap_nhat", width: colWidthByKey.ngay_cap_nhat},
  {...defaultTableColumnsProps, title: "Người cập nhật", dataIndex: "nguoi_cap_nhat", key: "nguoi_cap_nhat", width: colWidthByKey.nguoi_cap_nhat},
];

export const setFormFields = (form: any, chiTietHopDong: any) => {
  if (chiTietHopDong) {
    form.setFields([
      {
        name: "ten",
        value: chiTietHopDong.ten || "",
      },
      {
        name: "ma",
        value: chiTietHopDong.ma || "",
      },
      {
        name: "loai",
        value: chiTietHopDong.loai || "",
      },
      {
        name: "kieu_ad",
        value: chiTietHopDong.kieu_ad || "",
      },
    ]);
  }
};

export const radioItemTrangThaiHopDongTable = [
  {value: "Đã duyệt", text: "Đã duyệt"},
  {value: "Chưa duyệt", text: "Chưa duyệt"},
];

//option select trạng thái
export const optionTrangThaiHopDongSelect: Array<LabeledValue> = [
  {value: "D", label: "Đang sử dụng"},
  {value: "K", label: "Ngưng sử dụng"},
];

//Form
export interface IFormTimKiemPhanTrangHopDongFieldsConfig {
  ma_doi_tac_ql: IFormInput;
  ma_chi_nhanh_ql: IFormInput;
  san_pham: IFormInput;
  trang_thai: IFormInput;
  ngay_d: IFormInput;
  ngay_c: IFormInput;
  ten_doi_tuong: IFormInput;
  nd: IFormInput;
}
export const FormTimKiemPhanTrangHopDongBaoHiemTaiSan: IFormTimKiemPhanTrangHopDongFieldsConfig = {
  ma_doi_tac_ql: {
    component: "select",
    name: "ma_doi_tac_ql",
    label: "Đối tác",
    placeholder: "Chọn đối tác",
  },
  ma_chi_nhanh_ql: {
    component: "select",
    name: "ma_chi_nhanh_ql",
    label: "Chi nhánh",
    placeholder: "Chọn chi nhánh",
  },
  san_pham: {
    component: "select",
    name: "san_pham",
    label: "Sản phẩm",
    placeholder: "Chọn sản phẩm",
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    label: "Trạng thái",
    placeholder: "Chọn trạng thái",
  },
  ngay_d: {
    component: "date-picker",
    name: "ngay_d",
    label: "Từ ngày",
    placeholder: "Chọn ngày",
    className: "w-full",
  },
  ngay_c: {
    component: "date-picker",
    name: "ngay_c",
    label: "Đến ngày",
    placeholder: "Chọn ngày",
    className: "w-full",
  },
  ten_doi_tuong: {
    name: "ten_doi_tuong",
    // label: "Biển xe/số khung/số máy",
    placeholder: "Tên đối tượng",
  },
  nd: {
    name: "nd",
    // label: "Số HĐ/GCN/Tên khách hàng",
    placeholder: "Số HĐ/GCN/Tên khách hàng",
  },
};
//option select loại chức năng
export const optionLoaiChucNangData = [
  {value: "NHAP", label: "NHAP"},
  {value: "XEM", label: "XEM"},
];
//option select kiểu áp dụng
export const optionLoaiKieuApDungData = [
  {value: "K", label: "AD riêng cho từng tài khoản"},
  {value: "C", label: "AD chung cho tất cả tài khoản"},
];

// defaultFormValue tìm kiếm phân trang hđ xe
export const defaultFormValue: ReactQuery.ITimKiemPhanTrangHopDongBaoHiemTaiSanParams = {
  ma_chi_nhanh_ql: "",
  ma_doi_tac_ql: "",
  ngay_d: ngayDauNam.format("YYYYMMDD"),
  ngay_c: currentDate.format("YYYYMMDD"),
  nd: "",
  ma_sp: "",
  ten: "",

  //   ten_doi_tuong: "",
  trang_thai: "",
  trang: 1,
  so_dong: 13,
  nv: "TS",
};

export const defaultFormValueTimKiemDoiTuongBaoHiemXe: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams = {
  so_id: 0,
  // gcn: "",
  // ten: "",
  nd_tim: "",
  trang: 1,
  so_dong: 14, // set khác default cho vừa màn hình
  ma_dvi_dong_tai: "",
  dong_tai: "",
};

export const defaultFormValueTimKiemHangMucTonThat: ReactQuery.ITimKiemPhanTrangHangMucTonThatParams = {
  nv: "",
  ma: "",
  ten: "",
  loai: "",
  nhom: "",
  vi_tri: "",
  trang_thai: "",
  trang: 1,
  so_dong: 10,
};
