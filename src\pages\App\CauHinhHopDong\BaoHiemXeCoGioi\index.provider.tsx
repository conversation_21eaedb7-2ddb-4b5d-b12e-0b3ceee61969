import {ReactQuery} from "@src/@types";
import {ACTION_CODE} from "@src/constants";
import {FileEndpoint} from "@src/services/axios";
import {CommonExecuteResponse, useCommonExecute} from "@src/services/react-queries";
import {message} from "antd";
import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";
import {defaultFormValue, defaultFormValueTimKiemDoiTuongBaoHiemXe, NGHIEP_VU_XE} from "./index.configs";
import {BaoHiemXeCoGioiContext} from "./index.context";
import {BaoHiemXeCoGioiContextProps} from "./index.model";

/* 
  file Provider : Chứa logic quản lý state, gọi API, wrap context
*/

const BaoHiemXeCoGioiProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();
  const [danhSachHopDongXe, setDanhSachHopDongXe] = useState<Array<CommonExecute.Execute.IHopDongXe>>([]);
  const [danhSachDoiTuongBaoHiemXe, setDanhSachDoiTuongBaoHiemXe] = useState<Array<CommonExecute.Execute.IHopDongXe>>([]);
  const [danhSachKhachHang, setDanhSachKhachHang] = useState<Array<CommonExecute.Execute.IKhachHang>>([]);
  // const [chiTietHopDongBaoHiemXe, setChiTietHopDongBaoHiemXe] = useState<CommonExecute.Execute.IChiTietNhomChucNang>({});
  // const [chiTietDoiTuongBaoHiemXe, setChiTietDoiTuongBaoHiemXe] = useState<CommonExecute.Execute.IChiTietNhomChucNang>({});
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [tongSoDongDataLDaiLy, setTongSoDongDataLDaiLy] = useState<number>(0);
  const [tongSoDongDataKhachHang, setTongSoDongDataKhachHang] = useState<number>(0);
  const [tongSoDongDoiTuongBaoHiemXe, setTongSoDongDoiTuongBaoHiemXe] = useState<number>(0);
  const [tongPhiBaoHiemXeFromAPI, setTongPhiBaoHiemXeFromAPI] = useState<number>(0);
  const [tongSoDongCanBoQuanLy, setTongSoDongCanBoQuanLy] = useState<number>(0);
  const [filterParams, setFilterParams] = useState<ReactQuery.ILayDanhSachChucDanhPhanTrangParams>({ma: "", ma_doi_tac_ql: "", ten: "", trang_thai: "", trang: 1, so_dong: 20});
  const [danhSachDaiLy, setDanhSachDaiLy] = useState<Array<CommonExecute.Execute.IDanhMucDaiLy>>([]);
  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [listPhuongThucKhaiThac, setListPhuongThucKhaiThac] = useState<Array<CommonExecute.Execute.IPhuongThucKhaiThac>>([]);
  const [listSanPham, setListSanPham] = useState<Array<CommonExecute.Execute.ISanPham>>([]);
  const [listChiNhanh, setListChiNhanh] = useState<Array<CommonExecute.Execute.IChiNhanh>>([]);
  const [listPhongBan, setListPhongBan] = useState<Array<CommonExecute.Execute.IPhongBan>>([]);
  const [listDonViBoiThuong, setListDonViBoiThuong] = useState<Array<CommonExecute.Execute.INhaBaoHiemTPA>>([]);
  const [listChuongTrinhBaoHiem, setListChuongTrinhBaoHiem] = useState<Array<CommonExecute.Execute.IChuongTrinhBaoHiem>>([]);
  const [listCanBo, setListCanBo] = useState<Array<CommonExecute.Execute.IDoiTacNhanVien>>([]);
  const [listHangXe, setListHangXe] = useState<Array<CommonExecute.Execute.IHangXe>>([]);
  const [listLoaiXe, setListLoaiXe] = useState<Array<CommonExecute.Execute.ILoaiXe>>([]);
  const [listHieuXe, setListHieuXe] = useState<Array<CommonExecute.Execute.IHieuXe>>([]);
  const [listLoaiHinhNghiepVuXeCoGioi, setListLoaiHinhNghiepVuXeCoGioi] = useState<Array<CommonExecute.Execute.ILoaiHinhNghiepVuXeCoGioi>>([]);
  const [listDieuKhoanBoSungXeCoGioi, setListDieuKhoanBoSungXeCoGioi] = useState<Array<CommonExecute.Execute.ILoaiHinhNghiepVuXeCoGioi>>([]);
  const [listThongTinThanhToanHopDongXe, setListThongTinThanhToanHopDongXe] = useState<Array<CommonExecute.Execute.IThongTinThanhToanCuaHopDong>>([]);
  const [chiTietHopDongBaoHiemXe, setChiTietHopDongBaoHiemXe] = useState<CommonExecute.Execute.IHopDongXe>({});
  const [listDongBaoHiemHopDongXe, setListDongBaoHiemHopDongXe] = useState<Array<CommonExecute.Execute.IDongBaoHiem>>([]);
  const [listTaiBaoHiemHopDongXe, setListTaiBaoHiemHopDongXe] = useState<Array<CommonExecute.Execute.ITaiBaoHiem>>([]);
  const [listHangMucXeTheoNhom, setListHangMucXeTheoNhom] = useState<Array<CommonExecute.Execute.IHangMucXe>>([]);
  const [listDonViDongTai, setListDonViDongTai] = useState<Array<CommonExecute.Execute.IDonViDongTai>>([]);
  const [danhSachNguoiDuyetHopDong, setDanhSachNguoiDuyetHopDong] = useState<Array<CommonExecute.Execute.INguoiPheDuyetHopDongXe>>([]);
  const [listMucDoTonThatXe, setListMucDoTonThatXe] = useState<Array<CommonExecute.Execute.IMucDoTonThatXe>>([]);
  const [danhSachHangMucTonThat, setDanhSachHangMucTonThat] = useState<Array<any>>([]);
  const [tongSoDongHangMucTonThat, setTongSoDongHangMucTonThat] = useState<number>(0);
  const [chiTietDoiTuongBaoHiemXe, setChiTietDoiTuongBaoHiemXe] = useState<CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi>({});
  const [dataHangMucTonThatTheoDoiTuong, setDataHangMucTonThatTheoDoiTuong] = useState<CommonExecute.Execute.IHangMucXe[]>([]);

  useEffect(() => {
    initData();
  }, []);

  const initData = () => {
    layDanhSachHopDongBaoHiemXePhanTrang(defaultFormValue);
    getListDoiTac();
    getListPhuongThucKhaiThac({ma_doi_tac_ql: ""});
    getListSanPham({ma_doi_tac_ql: "", nv: ""});
    getListChiNhanh();
    getListPhongBan();
    getListDonViBoiThuong({nv: NGHIEP_VU_XE});
    getListChuongTrinHBaoHiem({nv: NGHIEP_VU_XE, ma_doi_tac_ql: ""});
    getListLoaiXe();
    getListHangXe();
    getListHieuXe();
    getListLoaiHinhNghiepVuXeCoGioi();
    getListDieuKhoanBoSungXeCoGioi();
    layDanhSachDonViDongTai();
    getListHangMucXeTheoNhom({nhom: "TOAN_CANH"});
    getListMucDoTonThatXe();
  };

  //DS HĐ xe cơ giới phân trang
  const layDanhSachHopDongBaoHiemXePhanTrang = useCallback(
    async (body: ReactQuery.ILayDanhSachHopDongXePhanTrangParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.GET_DS_HD_BH_XCG_PHAN_TRANG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        setDanhSachHopDongXe(data);
        setTongSoDong(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("layDanhSachNhomChucNangPhanTrang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //tìm kiếm phân trang khách hàng
  const timKiemPhanTrangKhachHang = useCallback(
    async (body: ReactQuery.ILayDanhSachKhachHangPhanTrangParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_KHACH_HANG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        setDanhSachKhachHang(data);
        setTongSoDongDataKhachHang(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("timKiemPhanTrangKhachHang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //Lấy chi tiết 1 hợp đồng bảo hiểm xecg
  const layChiTietHopDongBaoHiemXe = useCallback(
    async (item: ReactQuery.IChiTietHopDongXeParams): Promise<CommonExecute.Execute.IHopDongXe | null> => {
      try {
        const params = {
          so_id: item.so_id,
          ma_doi_tac_ql: item.ma_doi_tac_ql,
          actionCode: ACTION_CODE.GET_CHI_TIET_HD_BH_XCG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        setChiTietHopDongBaoHiemXe(responseData.data);
        return responseData.data as CommonExecute.Execute.IHopDongXe;
      } catch (error: any) {
        console.log("layChiTietChucNang error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  /* KHÁCH HÀNG */
  // TÌM KIẾM PHÂN TRANG KHÁCH HÀNG
  const searchKhachHang = useCallback(
    async (filterKhachHangParams: ReactQuery.ILayDanhSachKhachHangPhanTrangParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...filterKhachHangParams,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_KHACH_HANG,
        });
        return (
          response?.data || {
            data: [],
            tong_so_dong: 0,
          }
        );
      } catch (error) {
        console.log("searchHopDong error ", error);
        return {
          data: [],
          tong_so_dong: 0,
        };
      }
    },
    [mutateUseCommonExecute],
  );

  /* ĐỐI TÁC */
  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DOI_TAC,
      });
      // return response?.data || [];
      setListDoiTac(response?.data || []);
    } catch (error) {
      console.log("getListDoiTac error ", error);
      return [];
    }
  }, [mutateUseCommonExecute]);

  /* CHI NHÁNH */
  const getListChiNhanh = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_CHI_NHANH,
      });
      // return response?.data || {data: [], tong_so_dong: 0};
      setListChiNhanh(response?.data || []);
    } catch (error) {
      console.log("getListDoiTac error ", error);
      return {data: [], tong_so_dong: 0};
    }
  }, [mutateUseCommonExecute]);

  /* PHÒNG BAN */
  const getListPhongBan = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DANH_SACH_PHONG_BAN,
      });
      // return response?.data || {data: [], tong_so_dong: 0};
      setListPhongBan(response?.data || []);
    } catch (error) {
      console.log("getListPhongBan error ", error);
      return {data: [], tong_so_dong: 0};
    }
  }, [mutateUseCommonExecute]);

  /** CÁN BỘ QUẢN LÝ */
  const getListCanBoQuanLy = useCallback(
    async (params: ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams & ReactQuery.IPhanTrang) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_CAN_BO_QUAN_LY,
        });
        setListCanBo(response?.data?.data || []);
        setTongSoDongCanBoQuanLy(response?.data?.tong_so_dong || 0);
        return {
          data: response?.data?.data || [],
          tong_so_dong: response?.data?.tong_so_dong || 0,
        };
      } catch (error) {
        console.log("getListCanBoQuanLy error ", error);
        return {data: [], tong_so_dong: 0};
      }
    },
    [mutateUseCommonExecute],
  );

  // SẢN PHẨM
  const getListSanPham = useCallback(
    async (params: ReactQuery.ILietKeSanPhamParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_SAN_PHAM,
        });
        // return response || {data: []};
        setListSanPham(response?.data || []);
      } catch (error) {
        console.log("getListSanPham error ", error);
        return {data: []};
      }
    },
    [mutateUseCommonExecute],
  );

  // CHUONG TRÌNH BẢO HIỂM
  const getListChuongTrinHBaoHiem = useCallback(
    async (params: ReactQuery.ILietKeChuongTrinhBaoHiemParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_CHUONG_TRINH_BAO_HIEM,
        });
        // return response || {data: []};
        setListChuongTrinhBaoHiem(response?.data || []);
      } catch (error) {
        console.log("getListChuongTrinHBaoHiem error ", error);
        return {data: []};
      }
    },
    [mutateUseCommonExecute],
  );

  // PHƯƠNG THỨC KHAI THÁC
  const getListPhuongThucKhaiThac = useCallback(
    async (params: ReactQuery.ILietKePhuongThucKhaiThacParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LAY_DS_PHUONG_THUC_KHAI_THAC,
        });
        // return response || {data: []};
        setListPhuongThucKhaiThac(response?.data || []);
      } catch (error) {
        console.log("getListPhuongThucKhaiThac error ", error);
        return {data: []};
      }
    },
    [mutateUseCommonExecute],
  );

  // ĐẠI LÝ KHAI THÁC
  const getListDaiLyKhaiThac = useCallback(
    async (params: ReactQuery.ILietKeDanhSachDaiLyParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DAI_LY,
        });
        return response || {data: [], tong_so_dong: 0};
      } catch (error) {
        console.log("getListDaiLyKhaiThac error ", error);
        return {data: [], tong_so_dong: 0};
      }
    },
    [mutateUseCommonExecute],
  );

  // ĐƠN VỊ BỒI THƯỜNG
  const getListDonViBoiThuong = useCallback(
    async (params: ReactQuery.ILietKeDonViBoiThuongTPAParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DON_VI_BOI_THUONG_TPA,
        });
        // return response || {data: []};
        setListDonViBoiThuong(response?.data || []);
      } catch (error) {
        console.log("getListDonViBoiThuong error ", error);
        return {data: []};
      }
    },
    [mutateUseCommonExecute],
  );

  //DS đại lý phân trang
  const timKiemPhanTrangDaiLy = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDanhSachDaiLyParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DS_DAI_LY,
        };

        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);

        const data = response.data.data;
        setDanhSachDaiLy(data);
        setTongSoDongDataLDaiLy(response.data.tong_so_dong);
      } catch (error: any) {
        console.log("Lấy danh sách đại lý error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //update hđ
  const updateHopDongXe = useCallback(
    async (body: ReactQuery.IUpdateHopDongParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LUU_THONG_TIN_HOP_DONG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          // Kiểm tra xem đây là tạo mới hay cập nhật dựa trên so_id trong params
          const isCreatingNew = !body.so_id || body.so_id === 0;

          if (isCreatingNew && response.output?.so_id && response.output?.so_hd) {
            // Trường hợp tạo hợp đồng mới thành công
            message.success("Tạo hợp đồng thành công!");
            layDanhSachHopDongBaoHiemXePhanTrang(defaultFormValue);
            return {
              success: true,
              isNewContract: true,
              contractInfo: {
                so_id: response.output.so_id,
                so_hd: response.output.so_hd,
              },
            };
          } else {
            // Trường hợp cập nhật hợp đồng có sẵn
            message.success("Cập nhật thông tin hợp đồng thành công!");
            layDanhSachHopDongBaoHiemXePhanTrang(defaultFormValue);
            return {success: true, isNewContract: false};
          }
        } else return {success: false, isNewContract: false};
      } catch (error: any) {
        console.log("updateHopDongXe error ", error.message | error);
        return {success: false, isNewContract: false};
      }
    },
    [mutateUseCommonExecute],
  );
  //update hđ
  const updateDoiTuongBaoHiemXe = useCallback(
    async (body: ReactQuery.IFormCapNhatDoiTuongBaoHiemXeCoGioiParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_DOI_TUONG_BH_XCG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin đối tượng thành công!");
          const reloadParams = {
            ...defaultFormValueTimKiemDoiTuongBaoHiemXe,
            so_id: body.so_id,
          };
          timKiemPhanTrangDoiTuongBaoHiemXe(reloadParams);
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateHopDongXe error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  //tìm kiếm phân trang đối tượng bảo hiểm xecg
  const timKiemPhanTrangDoiTuongBaoHiemXe = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangDoiTuongBaoHiemXeParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_DOI_TUONG_BH_XCG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        setDanhSachDoiTuongBaoHiemXe(data);
        setTongSoDongDoiTuongBaoHiemXe(response.data.tong_so_dong);
        // Cập nhật tổng phí bảo hiểm từ API
        const tongPhiFromAPI = (response as any).output?.tong_phi;
        setTongPhiBaoHiemXeFromAPI(Number(tongPhiFromAPI));
        return data;
      } catch (error: any) {
        console.log("layDanhSachNhomChucNangPhanTrang error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  //tìm kiếm phân trang hạng mục tổn thất
  const timKiemPhanTrangHangMucTonThat = useCallback(
    async (body: ReactQuery.ITimKiemPhanTrangHangMucTonThatParams & ReactQuery.IPhanTrang) => {
      try {
        const params = {
          ...body,
          nv: chiTietHopDongBaoHiemXe.ma_sp || "",
          loai: "CHINH",
          trang_thai: "D",
          actionCode: ACTION_CODE.TIM_KIEM_PHAN_TRANG_HANG_MUC_TON_THAT,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        const data = response.data.data;
        setDanhSachHangMucTonThat(data);
        setTongSoDongHangMucTonThat(response.data.tong_so_dong);
        return data;
      } catch (error: any) {
        console.log("timKiemPhanTrangHangMucTonThat error ", error.message | error);
      }
    },
    [mutateUseCommonExecute],
  );

  // LOẠI XE
  const getListLoaiXe = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_LOAI_XE,
      });
      setListLoaiXe(response?.data || []);
    } catch (error) {
      console.log("getListLoaiXe error ", error);
      return {data: []};
    }
  }, [mutateUseCommonExecute]);

  // HÃNG XE
  const getListHangXe = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_HANG_XE,
      });
      setListHangXe(response?.data as Array<CommonExecute.Execute.IHangXe>);
    } catch (error) {
      console.log("getListHangXe error ", error);
      return {data: []};
    }
  }, [mutateUseCommonExecute]);

  // HIỆU XE
  const getListHieuXe = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_HIEU_XE,
      });
      setListHieuXe(response?.data || []);
    } catch (error) {
      console.log("getListHieuXe error ", error);
      return {data: []};
    }
  }, [mutateUseCommonExecute]);

  //Lấy chi tiết 1 hợp đồng bảo hiểm xecg
  const layChiTietDoiTuongBaoHiemXe = useCallback(
    async (body: ReactQuery.IChiTietDoiTuongBaoHiemXeParams): Promise<CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi | null> => {
      try {
        const params = {
          so_id: body.so_id || 0,
          so_id_dt: body.so_id_dt || 0,
          actionCode: ACTION_CODE.CHI_TIET_DOI_TUONG_BH_XCG,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        setChiTietDoiTuongBaoHiemXe(responseData.data);
        return responseData.data as CommonExecute.Execute.IDoiTuongBaoHiemXeCoGioi;
      } catch (error: any) {
        console.log("layChiTietChucNang error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  // Liệt kê loại hình nghiệp vụ xe cơ giới
  const getListLoaiHinhNghiepVuXeCoGioi = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_LHNV_XCG,
      });
      setListLoaiHinhNghiepVuXeCoGioi((response?.data as unknown as Array<CommonExecute.Execute.ILoaiHinhNghiepVuXeCoGioi>) || []);
    } catch (error) {
      console.log("getListLoaiHinhNghiepVuXeCoGioi error ", error);
      return {data: []};
    }
  }, [mutateUseCommonExecute]);
  // Liệt kê loại hình nghiệp vụ xe cơ giới
  const getListDieuKhoanBoSungXeCoGioi = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_DKBS_XCG,
      });
      setListDieuKhoanBoSungXeCoGioi((response?.data as unknown as Array<CommonExecute.Execute.ILoaiHinhNghiepVuXeCoGioi>) || []);
    } catch (error) {
      console.log("getListLoaiHinhNghiepVuXeCoGioi error ", error);
      return {data: []};
    }
  }, [mutateUseCommonExecute]);

  //Lấy thông tin thanh toán của hợp đồng bảo hiểm xe cơ giới
  const layThongTinThanhToanCuaHopDongBaoHiemXe = useCallback(
    async (body: ReactQuery.ILayThongTinThanhToanCuaHopDongBaoHiemParams): Promise<any> => {
      try {
        const params = {
          so_id: body.so_id || 0,
          actionCode: ACTION_CODE.LIET_KE_THONG_TIN_THANH_TOAN_CUA_HOP_DONG_BAO_HIEM,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        setListThongTinThanhToanHopDongXe(responseData.data);
        return responseData.data;
      } catch (error: any) {
        console.log("layThongTinThanhToanCuaHopDongBaoHiemXe error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //update kỳ thanh toán
  const updateKyThanhToan = useCallback(
    async (body: ReactQuery.IUpdateKyThanhToanParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_TTIN_KY_TTOAN_CUA_HOP_DONG_BAO_HIEM,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Lưu thông tin kỳ thanh toán thành công!");
          layThongTinThanhToanCuaHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateHopDongXe error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute, chiTietHopDongBaoHiemXe],
  );

  //get chi tiết kỳ thanh toán
  const layChiTietKyThanhToan = useCallback(
    async (body: ReactQuery.IChiTietKyThanhToanParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.XEM_CHI_TIET_TTIN_KY_TTOAN_CUA_HOP_DONG_BAO_HIEM,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        return responseData.data as CommonExecute.Execute.IKyThanhToan;
      } catch (error: any) {
        console.log("updateHopDongXe error ", error.message | error);
        return null;
      }
    },
    [mutateUseCommonExecute, chiTietHopDongBaoHiemXe],
  );

  //Lấy thông tin đồng của hợp đồng xe
  const layDanhSachCauHinhDongCuaHopDongBaoHiemXe = useCallback(
    async (body: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams): Promise<any> => {
      try {
        const params = {
          so_id: body.so_id || 0,
          actionCode: ACTION_CODE.LIET_KE_DS_CAU_HINH_DONG_CUA_HOP_DONG_BAO_HIEM,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        setListDongBaoHiemHopDongXe(responseData.data);
        return responseData.data;
      } catch (error: any) {
        console.log("layThongTinThanhToanCuaHopDongBaoHiemXe error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //Lưu/cập nhật cấu hình đồng bảo hiểm xe
  const updateCauHinhDongBaoHiem = useCallback(
    async (body: ReactQuery.IUpdateCauHinhDongBHParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CAU_HINH_DONG_CUA_HOP_DONG_BAO_HIEM,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Lưu thông tin cấu hình đồng bảo hiểm thành công!");
          layDanhSachCauHinhDongCuaHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateHopDongXe error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute, chiTietHopDongBaoHiemXe],
  );

  //Lấy danh sách đơn vị đồng tái
  const layDanhSachDonViDongTai = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        actionCode: ACTION_CODE.LIET_KE_MA_DV_DONG_TAI,
      });
      setListDonViDongTai((response?.data as unknown as Array<CommonExecute.Execute.IDonViDongTai>) || []);
    } catch (error) {
      console.log("layDanhSachDonViDongTai error ", error);
      return {data: []};
    }
  }, [mutateUseCommonExecute]);

  //get chi tiết thông tin đồng BH
  const layChiTietThongTinCauHinhDongBH = useCallback(
    async (body: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => {
      try {
        const params = {
          so_id: chiTietHopDongBaoHiemXe.so_id,
          ma_dvi_dong: body.ma_dvi_dong,
          actionCode: ACTION_CODE.XEM_CHI_TIET_TTIN_CAU_HINH_DONG_BH,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        return responseData.data as CommonExecute.Execute.IDongBaoHiem;
      } catch (error: any) {
        console.log("updateHopDongXe error ", error.message | error);
        return null;
      }
    },
    [mutateUseCommonExecute, chiTietHopDongBaoHiemXe],
  );

  //Xoá thông tin đồng BH
  const xoaThongTinDongBH = useCallback(
    async (body: ReactQuery.IChiTietThongTinCauHinhDongBHParams) => {
      try {
        const params = {
          so_id: chiTietHopDongBaoHiemXe.so_id,
          ma_dvi_dong: body.ma_dvi_dong,
          actionCode: ACTION_CODE.XOA_TTIN_CAU_HINH_DONG_BH,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Xoá thông tin đồng BH thành công!");
          layDanhSachCauHinhDongCuaHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateHopDongXe error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute, chiTietHopDongBaoHiemXe],
  );

  //Lấy thông tin đồng của hợp đồng xe
  const layDanhSachCauHinhTaiBHCuaHopDongBaoHiemXe = useCallback(
    async (body: ReactQuery.ILayThongTinDongTaiBHCuaHopDongBaoHiemParams): Promise<any> => {
      try {
        const params = {
          so_id: body.so_id || 0,
          actionCode: ACTION_CODE.LIET_KE_DS_CAU_HINH_TAI_BH_CUA_HOP_DONG_BAO_HIEM,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        setListTaiBaoHiemHopDongXe(responseData.data);
        return responseData.data;
      } catch (error: any) {
        console.log("layThongTinThanhToanCuaHopDongBaoHiemXe error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //Lưu/cập nhật cấu hình tái bảo hiểm xe
  const updateCauHinhTaiBaoHiem = useCallback(
    async (body: ReactQuery.IUpdateCauHinhTaiBHParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_CAU_HINH_TAI_BH_CUA_HOP_DONG_BAO_HIEM,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Lưu thông tin cấu hình tái bảo hiểm thành công!");
          layDanhSachCauHinhTaiBHCuaHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateCauHinhTaiBaoHiem error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute, chiTietHopDongBaoHiemXe],
  );

  //Xoá thông tin tái BH
  const xoaThongTinTaiBH = useCallback(
    async (body: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => {
      try {
        const params = {
          so_id: chiTietHopDongBaoHiemXe.so_id,
          ma_dvi_tai: body.ma_dvi_tai,
          actionCode: ACTION_CODE.XOA_TTIN_CAU_HINH_TAI_BH,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Xoá thông tin tái BH thành công!");
          layDanhSachCauHinhTaiBHCuaHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("xoaThongTinTaiBH error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute, chiTietHopDongBaoHiemXe],
  );

  //get chi tiết thông tin đồng BH
  const layChiTietThongTinCauHinhTaiBH = useCallback(
    async (body: ReactQuery.IChiTietThongTinCauHinhTaiBHParams) => {
      try {
        const params = {
          so_id: chiTietHopDongBaoHiemXe.so_id,
          ma_dvi_tai: body.ma_dvi_tai,
          actionCode: ACTION_CODE.XEM_CHI_TIET_TTIN_CAU_HINH_TAI_BH,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        return responseData.data as CommonExecute.Execute.ITaiBaoHiem;
      } catch (error: any) {
        console.log("layChiTietThongTinCauHinhTaiBH error ", error.message | error);
        return null;
      }
    },
    [mutateUseCommonExecute, chiTietHopDongBaoHiemXe],
  );

  //update đối tượng áp dụng tl đồng BH
  const updateDoiTuongApDungTyLeDongBH = useCallback(
    async (body: ReactQuery.IUpdateDoiTuongApDungDongBHParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_DOI_TUONG_AP_DUNG_TL_DONG_BH,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin đối tượng áp dụng thành công!");
          layDanhSachCauHinhDongCuaHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe?.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateDoiTuongApDungTyLeDongBH error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  //  update đối tượng áp dụng tái BH
  const updateDoiTuongApDungTaiBH = useCallback(
    async (body: ReactQuery.IUpdateDoiTuongApDungTaiBHParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_DOI_TUONG_AP_DUNG_TAI_BH,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Cập nhật thông tin đối tượng áp dụng thành công!");
          layDanhSachCauHinhTaiBHCuaHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe?.so_id});
          return true;
        } else return false;
      } catch (error: any) {
        console.log("updateDoiTuongApDungTyLeDongBH error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  const lietKeDanhSachCacDoiTuongDaDuocApDungDongBH = useCallback(
    async (body: ReactQuery.IChiTietThongTinCauHinhDongBHParams): Promise<any> => {
      try {
        const params = {
          so_id: chiTietHopDongBaoHiemXe.so_id || 0,
          ma_dvi_dong: body.ma_dvi_dong,
          actionCode: ACTION_CODE.LIET_KE_DS_CAC_DOI_TUONG_DA_DUOC_AD_DONG_BAO_HIEM,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        return responseData.data;
      } catch (error: any) {
        console.log("lietKeDanhSachCacDoiTuongDaDuocApDungDongBH error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  //
  const lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH = useCallback(
    async (body: ReactQuery.IChiTietThongTinCauHinhTaiBHParams): Promise<any> => {
      try {
        const params = {
          so_id: chiTietHopDongBaoHiemXe.so_id || 0,
          ma_dvi_tai: body.ma_dvi_tai,
          actionCode: ACTION_CODE.LIET_KE_DS_CAC_DOI_TUONG_DA_DUOC_AD_TAI_BAO_HIEM,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        return responseData.data;
      } catch (error: any) {
        console.log("lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );
  //Lấy thông tin thanh toán của hợp đồng bảo hiểm xe cơ giới
  const getDanhSachNguoiDuyetHopDong = useCallback(
    async (body: ReactQuery.ILietKeDanhSachNguoiDuyetParams): Promise<any> => {
      try {
        const params = {
          so_id: body.so_id || 0,
          nd_tim: body.nd_tim,
          nhom_duyet: "HOP_DONG",
          actionCode: ACTION_CODE.LIET_KE_DS_NGUOI_DUYET_HD,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        setDanhSachNguoiDuyetHopDong(responseData?.data || []);
        return responseData.data || [];
      } catch (error: any) {
        console.log("layThongTinThanhToanCuaHopDongBaoHiemXe error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //Huỷ HĐ XE
  const huyHopDongXe = useCallback(async () => {
    try {
      const params = {
        so_id: chiTietHopDongBaoHiemXe.so_id,
        ma_doi_tac_ql: chiTietHopDongBaoHiemXe.ma_doi_tac_ql,
        actionCode: ACTION_CODE.HUY_HOP_DONGD_BH_XCG,
      };
      const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if ((response.data as unknown as number) === -1) {
        message.success("Huỷ hợp đồng BH thành công!");
        layChiTietHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe.so_id, ma_doi_tac_ql: chiTietHopDongBaoHiemXe.ma_doi_tac_ql});
        layDanhSachHopDongBaoHiemXePhanTrang(defaultFormValue);
        return true;
      } else return false;
    } catch (error: any) {
      console.log("xoaThongTinTaiBH error ", error.message | error);
      return false;
    }
  }, [mutateUseCommonExecute]);

  //Gỡ Huỷ HĐ XE
  const goHuyHopDongXe = useCallback(async () => {
    try {
      const params = {
        so_id: chiTietHopDongBaoHiemXe.so_id,
        ma_doi_tac_ql: chiTietHopDongBaoHiemXe.ma_doi_tac_ql,
        actionCode: ACTION_CODE.GO_HUY_HOP_DONG_BH_XCG,
      };
      const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if ((response.data as unknown as number) === -1) {
        message.success("Gỡ huỷ hợp đồng BH thành công!");
        layChiTietHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe.so_id, ma_doi_tac_ql: chiTietHopDongBaoHiemXe.ma_doi_tac_ql});
        layDanhSachHopDongBaoHiemXePhanTrang(defaultFormValue);
        return true;
      } else return false;
    } catch (error: any) {
      console.log("goHuyHopDongXe error ", error.message | error);
      return false;
    }
  }, [mutateUseCommonExecute]);

  //Trình phê duyệt HĐXCG
  const trinhPheDuyetHopDong = useCallback(
    async (body: ReactQuery.ITrinhPheDuyetHopDongParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.TRINH_PHE_DUYET_HD_XCG,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Trình phê duyệt hợp đồng BH thành công!");
          layChiTietHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe.so_id, ma_doi_tac_ql: chiTietHopDongBaoHiemXe.ma_doi_tac_ql});
          layDanhSachHopDongBaoHiemXePhanTrang(defaultFormValue);
          return true;
        } else return false;
      } catch (error: any) {
        console.log("trinhPheDuyetHopDong error ", error.message | error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );
  //Huỷ Trình phê duyệt HĐXCG
  const huyTrinhPheDuyetHopDong = useCallback(async () => {
    try {
      const params = {
        nv: chiTietHopDongBaoHiemXe.nv,
        so_id: chiTietHopDongBaoHiemXe.so_id,
        actionCode: ACTION_CODE.HUY_TRINH_PHE_DUYET_HD_XCG,
      };
      const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if ((response.data as unknown as number) === -1) {
        message.success("Huỷ trình phê duyệt hợp đồng BH thành công!");
        layChiTietHopDongBaoHiemXe({so_id: chiTietHopDongBaoHiemXe.so_id, ma_doi_tac_ql: chiTietHopDongBaoHiemXe.ma_doi_tac_ql});
        layDanhSachHopDongBaoHiemXePhanTrang(defaultFormValue);
        return true;
      } else return false;
    } catch (error: any) {
      console.log("huyTrinhPheDuyetHopDong error ", error.message | error);
      return false;
    }
  }, [mutateUseCommonExecute]);

  //get chi tiết thông tin đồng BH
  const getDanhSachFileThumbnailTheoDoiTuong = useCallback(
    async (body: ReactQuery.IGetFileThumbnailParams) => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.GET_FILE_THUMBNAIL,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        return responseData.data as CommonExecute.Execute.IFileThumbnail;
      } catch (error: any) {
        console.log("getDanhSachFileThumbnailTheoDoiTuong error ", error.message | error);
        return null;
      }
    },
    [mutateUseCommonExecute, chiTietHopDongBaoHiemXe],
  );

  //Upload file theo đối tượng
  const uploadFileTheoDoiTuong = useCallback(async (body: ReactQuery.IUploadFileTheoDoiTuongXeParams): Promise<boolean> => {
    try {
      const params = {
        ...body,
        actionCode: ACTION_CODE.UPLOAD_FILE_THEO_DOI_TUONG,
      };
      const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if ((responseData.data as unknown as number) === -1) {
        message.success("Upload file thành công!");
        return true;
      } else {
        message.error("Upload file thất bại!");
        return false;
      }
    } catch (error: any) {
      console.log("uploadFileTheoDoiTuong error ", error.message | error);
      message.error("Upload file thất bại!");
      return false;
    }
  }, []);

  //Xoá file theo đối tượng
  const deleteFileTheoDoiTuong = useCallback(async (body: ReactQuery.IUploadFileTheoDoiTuongXeParams): Promise<boolean> => {
    try {
      const params = {
        ...body,
        actionCode: ACTION_CODE.XOA_FILE_THEO_DOI_TUONG, // đúng constant
      };
      const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if ((responseData.data as unknown as number) === -1) {
        message.success("Xoá file thành công!");
        return true;
      } else {
        message.error("Xoá file thất bại!");
        return false;
      }
    } catch (error: any) {
      console.log("deleteFileTheoDoiTuong error ", error.message | error);
      message.error("Xoá file thất bại!");
      return false;
    }
  }, []);

  //Phân loại file theo đối tượng
  const phanLoaiFileTheoHangMucXe = useCallback(async (body: ReactQuery.IPhanLoaiFileTheoHangMucXeParams): Promise<boolean> => {
    try {
      const params = {
        ...body,
        actionCode: ACTION_CODE.PHAN_LOAI_FILE_THEO_HANG_MUC_XE,
      };
      const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      if ((responseData.data as unknown as number) === -1) {
        message.success("Phân loại file thành công!");
        return true;
      } else {
        return false;
      }
    } catch (error: any) {
      message.error("Phân loại file thất bại!");
      return false;
    }
  }, []);

  //LK DS HẠNG MỤC XE THEO NHÓM
  const getListHangMucXeTheoNhom = useCallback(
    async (body: ReactQuery.ILietKeHangMucXeTheoNhomParams): Promise<any> => {
      try {
        const params = {
          nhom: body.nhom || "",
          actionCode: ACTION_CODE.LIET_KE_DS_HANG_MUC_THEO_NHOM,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        setListHangMucXeTheoNhom(responseData.data);
        return responseData.data;
      } catch (error: any) {
        console.log("getListHangMucXeTheoNhom error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  //LK DS MỨC ĐỘ TỔN THẤT XE
  const getListMucDoTonThatXe = useCallback(async (): Promise<any> => {
    try {
      const params = {
        actionCode: ACTION_CODE.LIET_KE_MUC_DO_TON_THAT_XE,
      };
      const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
      setListMucDoTonThatXe(responseData.data);
      return responseData.data;
    } catch (error: any) {
      console.log("getListMucDoTonThatXe error ", error.message || error);
      return null;
    }
  }, [mutateUseCommonExecute]);

  //Lấy danh sách hạng mục tổn thất theo đối tượng xe
  const layDanhSachHangMucTonThatTheoDoiTuongXe = useCallback(
    async (params: ReactQuery.IChiTietDoiTuongBaoHiemXeParams): Promise<any> => {
      try {
        const requestParams = {
          so_id: params.so_id || 0,
          so_id_dt: params.so_id_dt || 0,
          actionCode: ACTION_CODE.LIET_KE_DANH_SACH_DANH_GIA_TON_THAT,
        };
        const responseData: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(requestParams);

        setDataHangMucTonThatTheoDoiTuong(responseData.data);
        return responseData.data;
      } catch (error: any) {
        console.log("layDanhSachHangMucTonThatTheoDoiTuongXe error ", error.message || error);
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  // Lưu hạng mục tổn thất
  const luuHangMucTonThat = useCallback(
    async (body: ReactQuery.ILuuHangMucTonThatParams): Promise<boolean> => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.LUU_HANG_MUC_TON_THAT,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Lưu hạng mục tổn thất thành công!");
          return true;
        } else {
          return false;
        }
      } catch (error: any) {
        message.error("Thông báo", error.message || error);
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  // Cập nhật hạng mục tổn thất xe
  const luuDanhGiaTonThatXe = useCallback(
    async (body: ReactQuery.ILuuDanhGiaTonThatParams): Promise<boolean> => {
      try {
        const params = {
          ...body,
          actionCode: ACTION_CODE.CAP_NHAT_HANG_MUC_TON_THAT_XE,
        };
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync(params);
        if ((response.data as unknown as number) === -1) {
          message.success("Cập nhật đánh giá tổn thất thành công!");
          return true;
        } else {
          return false;
        }
      } catch (error: any) {
        message.error("Cập nhật đánh giá tổn thất thất bại!");
        return false;
      }
    },
    [mutateUseCommonExecute],
  );

  // Thêm hàm tạo SĐBS vào đây
  const taoHopDongSuaDoiBoSung = useCallback(
    async (params: ReactQuery.ITaoHopDongSuaDoiBoSungParams): Promise<{so_id: number; so_hd: string} | null> => {
      try {
        const response: CommonExecuteResponse = await mutateUseCommonExecute.mutateAsync({
          ...params,
          actionCode: ACTION_CODE.TAO_HOP_DONG_SDBS_XCG,
        });
        if ((response.data as unknown as number) === -1) {
          message.success("Tạo hợp đồng SĐBS thành công!");
          return {
            so_id: response.output?.so_id || 0,
            so_hd: response.output?.so_hd || "",
          };
        } else {
          return null;
        }
      } catch (error) {
        console.log("taoHopDongSuaDoiBoSung error", error);
        message.error("Đã có lỗi xảy ra khi tạo hợp đồng SĐBS!");
        return null;
      }
    },
    [mutateUseCommonExecute],
  );

  // Export PDF hợp đồng
  const exportPdfHopDong = useCallback(async (params: ReactQuery.IExportPDFHopDongParams): Promise<string | null> => {
    const actionCode = params.so_id_dt ? ACTION_CODE.EXPORT_PDF_GCN_XCG : ACTION_CODE.EXPORT_PDF_HOP_DONG;
    try {
      const requestParams = {
        ...params,
        actionCode,
      };
      const blob = await FileEndpoint.exportPdfHopDong(requestParams);

      // Kiểm tra blob có hợp lệ không
      if (!blob || blob.size === 0) {
        throw new Error("PDF blob is empty or invalid");
      }

      // Kiểm tra kích thước blob có hợp lý không (PDF thường > 1KB)
      if (blob.size < 1000) {
        console.warn("⚠️ Blob size is very small:", blob.size, "bytes");

        // Thử đọc nội dung blob để debug
        try {
          const text = await blob.text();
          console.log("🔍 Blob content (first 500 chars):", text.substring(0, 500));

          // Nếu blob chứa JSON error message
          if (text.startsWith("{") || text.startsWith("[")) {
            const errorData = JSON.parse(text);
            message.error(`Lỗi từ server: ${errorData.message || errorData.error_message || "Unknown error"}`);
            return null;
          }
        } catch (parseError) {
          console.log("🔍 Could not parse blob as text/JSON");
        }

        throw new Error(`PDF file too small (${blob.size} bytes). This might be an error response.`);
      }

      // Kiểm tra blob có phải là PDF không
      if (blob.type && !blob.type.includes("pdf") && !blob.type.includes("octet-stream")) {
        console.warn("⚠️ Blob type is not PDF:", blob.type);
      }

      // Tạo URL từ blob để hiển thị PDF
      const pdfUrl = URL.createObjectURL(blob);
      return pdfUrl;
    } catch (error) {
      console.error("❌ exportPdfHopDong error:", error);
      message.error("Không thể tải file PDF!");
      return null;
    }
  }, []);

  // Export Excel danh sách đối tượng
  const exportExcel = useCallback(async (params: ReactQuery.IExportExcelParams): Promise<string | null> => {
    try {
      const requestParams = {
        ...params,
        actionCode: ACTION_CODE.EXPORT_EXCEL,
      };
      const blob = await FileEndpoint.exportExcel(requestParams);

      // Kiểm tra blob có hợp lệ không
      if (!blob || blob.size === 0) {
        throw new Error("Excel blob is empty or invalid");
      }

      // Kiểm tra kích thước blob có hợp lý không (Excel thường > 1KB)
      if (blob.size < 1000) {
        console.warn("⚠️ Blob size is very small:", blob.size, "bytes");

        // Thử đọc nội dung blob để debug
        try {
          const text = await blob.text();
          console.log("🔍 Blob content (first 500 chars):", text.substring(0, 500));

          // Nếu blob chứa JSON error message
          if (text.startsWith("{") || text.startsWith("[")) {
            const errorData = JSON.parse(text);
            message.error(`Lỗi từ server: ${errorData.message || errorData.error_message || "Unknown error"}`);
            return null;
          }
        } catch (parseError) {
          console.log("🔍 Could not parse blob as text/JSON");
        }

        throw new Error(`Excel file too small (${blob.size} bytes). This might be an error response.`);
      }

      // Kiểm tra blob có phải là Excel không
      if (blob.type && !blob.type.includes("spreadsheet") && !blob.type.includes("excel") && !blob.type.includes("octet-stream")) {
        console.warn("⚠️ Blob type is not Excel:", blob.type);
      }

      // Tạo URL từ blob để download Excel
      const excelUrl = URL.createObjectURL(blob);

      // Tự động download file
      const link = document.createElement("a");
      link.href = excelUrl;
      link.download = `DanhSachDoiTuong_${params.so_id}_${new Date().getTime()}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Cleanup URL
      setTimeout(() => URL.revokeObjectURL(excelUrl), 100);

      // message.success("Xuất file Excel thành công!");
      return excelUrl;
    } catch (error) {
      console.error("❌ exportExcel error:", error);
      message.error("Không thể xuất file Excel!");
      return null;
    }
  }, []);

  // Hàm reset dữ liệu chi tiết hợp đồng và đối tượng bảo hiểm khi đóng modal
  const resetChiTietHopDongBaoHiemXe = useCallback(() => {
    setChiTietHopDongBaoHiemXe({});
    setChiTietDoiTuongBaoHiemXe({});
    // Reset các danh sách liên quan
    setListThongTinThanhToanHopDongXe([]);
    setListDongBaoHiemHopDongXe([]);
    setListTaiBaoHiemHopDongXe([]);
    setDataHangMucTonThatTheoDoiTuong([]);
  }, []);

  //khai báo giá trị của context để truyền vào Component
  const value = useMemo<BaoHiemXeCoGioiContextProps>(
    () => ({
      danhSachHopDongBaoHiemXeCoGioi: danhSachHopDongXe,
      loading: mutateUseCommonExecute.isLoading,
      tongSoDong,
      layDanhSachHopDongBaoHiemXePhanTrang,
      layChiTietHopDongBaoHiemXe,
      defaultFormValue,
      danhSachKhachHang,
      timKiemPhanTrangKhachHang,
      searchKhachHang,
      getListCanBoQuanLy,
      getListDaiLyKhaiThac,
      setFilterParams,
      timKiemPhanTrangDaiLy,
      danhSachDaiLy,
      updateHopDongXe,
      updateDoiTuongBaoHiemXe,
      timKiemPhanTrangDoiTuongBaoHiemXe,
      danhSachDoiTuongBaoHiemXe,
      tongSoDongDoiTuongBaoHiemXe,
      tongPhiBaoHiemXeFromAPI,
      tongSoDongCanBoQuanLy,
      listDoiTac,
      listPhuongThucKhaiThac,
      listSanPham,
      listChiNhanh,
      listPhongBan,
      listDonViBoiThuong,
      listChuongTrinhBaoHiem,
      listCanBo,
      listLoaiXe,
      listHangXe,
      listHieuXe,
      layChiTietDoiTuongBaoHiemXe,
      listLoaiHinhNghiepVuXeCoGioi,
      listDieuKhoanBoSungXeCoGioi,
      layThongTinThanhToanCuaHopDongBaoHiemXe,
      listThongTinThanhToanHopDongXe,
      chiTietHopDongBaoHiemXe,
      taoHopDongSuaDoiBoSung,
      updateKyThanhToan,
      layChiTietKyThanhToan,
      tongSoDongDataKhachHang,
      tongSoDongDataLDaiLy,
      layDanhSachCauHinhDongCuaHopDongBaoHiemXe,
      listDongBaoHiemHopDongXe,
      updateCauHinhDongBaoHiem,
      listDonViDongTai,
      layChiTietThongTinCauHinhDongBH,
      xoaThongTinDongBH,
      layDanhSachCauHinhTaiBHCuaHopDongBaoHiemXe,
      listTaiBaoHiemHopDongXe,
      updateCauHinhTaiBaoHiem,
      xoaThongTinTaiBH,
      layChiTietThongTinCauHinhTaiBH,
      updateDoiTuongApDungTyLeDongBH,
      updateDoiTuongApDungTaiBH,
      lietKeDanhSachCacDoiTuongDaDuocApDungDongBH,
      lietKeDanhSachCacDoiTuongDaDuocApDungTaiBH,
      getDanhSachNguoiDuyetHopDong,
      danhSachNguoiDuyetHopDong,
      huyHopDongXe,
      goHuyHopDongXe,
      trinhPheDuyetHopDong,
      huyTrinhPheDuyetHopDong,
      getDanhSachFileThumbnailTheoDoiTuong,
      uploadFileTheoDoiTuong,
      deleteFileTheoDoiTuong,
      phanLoaiFileTheoHangMucXe,
      getListHangMucXeTheoNhom,
      listHangMucXeTheoNhom,
      getListMucDoTonThatXe,
      listMucDoTonThatXe,
      layDanhSachHangMucTonThatTheoDoiTuongXe,
      timKiemPhanTrangHangMucTonThat,
      danhSachHangMucTonThat,
      tongSoDongHangMucTonThat,
      luuHangMucTonThat,
      luuDanhGiaTonThatXe,
      chiTietDoiTuongBaoHiemXe,
      dataHangMucTonThatTheoDoiTuong,
      exportPdfHopDong,
      exportExcel,
      resetChiTietHopDongBaoHiemXe,
    }),
    [
      danhSachHopDongXe,
      mutateUseCommonExecute,
      tongSoDong,
      layDanhSachHopDongBaoHiemXePhanTrang,
      layChiTietHopDongBaoHiemXe,
      danhSachKhachHang,
      timKiemPhanTrangKhachHang,
      searchKhachHang,
      getListCanBoQuanLy,
      getListDaiLyKhaiThac,
      getListDonViBoiThuong,
      setFilterParams,
      timKiemPhanTrangDaiLy,
      danhSachDaiLy,
      updateHopDongXe,
      updateDoiTuongBaoHiemXe,
      timKiemPhanTrangDoiTuongBaoHiemXe,
      danhSachDoiTuongBaoHiemXe,
      tongSoDongDoiTuongBaoHiemXe,
      tongPhiBaoHiemXeFromAPI,
      tongSoDongCanBoQuanLy,
      listDoiTac,
      listPhuongThucKhaiThac,
      listSanPham,
      listChiNhanh,
      listPhongBan,
      listDonViBoiThuong,
      listChuongTrinhBaoHiem,
      listCanBo,
      listLoaiXe,
      listHangXe,
      listHieuXe,
      layChiTietDoiTuongBaoHiemXe,
      listLoaiHinhNghiepVuXeCoGioi,
      listDieuKhoanBoSungXeCoGioi,
      layThongTinThanhToanCuaHopDongBaoHiemXe,
      listThongTinThanhToanHopDongXe,
      chiTietHopDongBaoHiemXe,
      taoHopDongSuaDoiBoSung,
      updateKyThanhToan,
      layChiTietKyThanhToan,
      tongSoDongDataKhachHang,
      tongSoDongDataLDaiLy,
      layDanhSachCauHinhDongCuaHopDongBaoHiemXe,
      listDongBaoHiemHopDongXe,
      updateCauHinhDongBaoHiem,
      listDonViDongTai,
      layChiTietThongTinCauHinhDongBH,
      layDanhSachCauHinhTaiBHCuaHopDongBaoHiemXe,
      listTaiBaoHiemHopDongXe,
      updateCauHinhTaiBaoHiem,
      layChiTietThongTinCauHinhTaiBH,
      updateDoiTuongApDungTyLeDongBH,
      updateDoiTuongApDungTaiBH,
      lietKeDanhSachCacDoiTuongDaDuocApDungDongBH,
      getDanhSachNguoiDuyetHopDong,
      danhSachNguoiDuyetHopDong,
      huyHopDongXe,
      goHuyHopDongXe,
      trinhPheDuyetHopDong,
      huyTrinhPheDuyetHopDong,
      getDanhSachFileThumbnailTheoDoiTuong,
      uploadFileTheoDoiTuong,
      deleteFileTheoDoiTuong,
      phanLoaiFileTheoHangMucXe,
      getListMucDoTonThatXe,
      layDanhSachHangMucTonThatTheoDoiTuongXe,
      listHangMucXeTheoNhom,
      listMucDoTonThatXe,
      timKiemPhanTrangHangMucTonThat,
      danhSachHangMucTonThat,
      tongSoDongHangMucTonThat,
      luuHangMucTonThat,
      luuDanhGiaTonThatXe,
      chiTietDoiTuongBaoHiemXe,
      dataHangMucTonThatTheoDoiTuong,
      exportPdfHopDong,
      exportExcel,
      resetChiTietHopDongBaoHiemXe,
    ],
  );

  return <BaoHiemXeCoGioiContext.Provider value={value}>{children}</BaoHiemXeCoGioiContext.Provider>;
};

export default BaoHiemXeCoGioiProvider;
